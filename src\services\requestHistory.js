const redis = require('redis');
const config = require('../config/environment');
const logger = require('../utils/logger');

class RequestHistoryService {
  constructor() {
    this.redisClient = null;
    this.keyPrefix = 'join_history:';
    this.indexPrefix = 'join_index:';
    this.initializeRedis();
  }

  async initializeRedis() {
    try {
      this.redisClient = redis.createClient({
        url: config.redis.url,
        password: config.redis.password
      });
      
      this.redisClient.on('error', (err) => {
        logger.error('Request History Redis connection error:', err);
      });
      
      await this.redisClient.connect();
      logger.info('Request History Redis connected successfully');
    } catch (error) {
      logger.error('Failed to connect Request History to Redis:', error);
      this.redisClient = null;
    }
  }

  /**
   * Store a join request and its result
   * @param {Object} requestData - The join request data
   * @param {Object} result - The result of the join request
   * @param {string} ip - Client IP address
   */
  async storeJoinRequest(requestData, result, ip) {
    if (!this.redisClient) {
      logger.error('Redis client not available for request history storage');
      return;
    }

    try {
      logger.info('Attempting to store join request in history', {
        peerId: requestData.peerId ? requestData.peerId.substring(0, 8) + '...' : 'undefined',
        account: requestData.account ? requestData.account.substring(0, 6) + '...' : 'undefined',
        poolId: requestData.poolId,
        chain: requestData.chain,
        success: result.success
      });
      const timestamp = new Date().toISOString();
      const requestId = `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      const historyEntry = {
        requestId,
        timestamp,
        ip,
        request: {
          peerId: requestData.peerId,
          account: requestData.account,
          chain: requestData.chain,
          poolId: requestData.poolId
        },
        result: {
          success: result.success,
          transactionHash: result.transactionHash || null,
          error: result.error || null,
          statusCode: result.success ? 200 : 400
        }
      };

      // Store the complete entry
      const entryKey = `${this.keyPrefix}${requestId}`;
      await this.redisClient.setex(entryKey, 2592000, JSON.stringify(historyEntry)); // 30 days TTL

      // Create indexes for quick lookup
      await this.createIndexes(requestData, requestId, timestamp);

      logger.info('Join request stored in history', {
        requestId,
        peerId: requestData.peerId, // Store full peerId
        account: requestData.account, // Store full account
        poolId: requestData.poolId, // Store full poolId
        chain: requestData.chain,
        success: result.success
      });

    } catch (error) {
      logger.error('Failed to store join request history:', error);
    }
  }

  /**
   * Create indexes for efficient querying
   */
  async createIndexes(requestData, requestId, timestamp) {
    const ttl = 2592000; // 30 days

    // Index by peerId
    const peerIdKey = `${this.indexPrefix}peer:${requestData.peerId}`;
    await this.redisClient.zadd(peerIdKey, Date.now(), requestId);
    await this.redisClient.expire(peerIdKey, ttl);

    // Index by account
    const accountKey = `${this.indexPrefix}account:${requestData.account.toLowerCase()}`;
    await this.redisClient.zadd(accountKey, Date.now(), requestId);
    await this.redisClient.expire(accountKey, ttl);

    // Index by chain
    const chainKey = `${this.indexPrefix}chain:${requestData.chain}`;
    await this.redisClient.zadd(chainKey, Date.now(), requestId);
    await this.redisClient.expire(chainKey, ttl);

    // Index by poolId
    const poolKey = `${this.indexPrefix}pool:${requestData.poolId}`;
    await this.redisClient.zadd(poolKey, Date.now(), requestId);
    await this.redisClient.expire(poolKey, ttl);

    // Global timeline index
    const timelineKey = `${this.indexPrefix}timeline`;
    await this.redisClient.zadd(timelineKey, Date.now(), requestId);
    await this.redisClient.expire(timelineKey, ttl);
  }

  /**
   * Query requests by peerId
   */
  async getRequestsByPeerId(peerId, limit = 50) {
    if (!this.redisClient) return [];

    try {
      const indexKey = `${this.indexPrefix}peer:${peerId}`;
      const requestIds = await this.redisClient.zRevRange (indexKey, 0, limit - 1);
      return await this.getRequestsByIds(requestIds);
    } catch (error) {
      logger.error('Failed to query requests by peerId:', error);
      return [];
    }
  }

  /**
   * Query requests by account
   */
  async getRequestsByAccount(account, limit = 50) {
    if (!this.redisClient) return [];

    try {
      const indexKey = `${this.indexPrefix}account:${account.toLowerCase()}`;
      const requestIds = await this.redisClient.zrevrange(indexKey, 0, limit - 1);
      return await this.getRequestsByIds(requestIds);
    } catch (error) {
      logger.error('Failed to query requests by account:', error);
      return [];
    }
  }

  /**
   * Query recent requests
   */
  async getRecentRequests(limit = 50) {
    if (!this.redisClient) return [];

    try {
      const timelineKey = `${this.indexPrefix}timeline`;
      const requestIds = await this.redisClient.zrevrange(timelineKey, 0, limit - 1);
      return await this.getRequestsByIds(requestIds);
    } catch (error) {
      logger.error('Failed to query recent requests:', error);
      return [];
    }
  }

  /**
   * Get requests by chain
   */
  async getRequestsByChain(chain, limit = 50) {
    if (!this.redisClient) return [];

    try {
      const indexKey = `${this.indexPrefix}chain:${chain}`;
      const requestIds = await this.redisClient.zrevrange(indexKey, 0, limit - 1);
      return await this.getRequestsByIds(requestIds);
    } catch (error) {
      logger.error('Failed to query requests by chain:', error);
      return [];
    }
  }

  /**
   * Get requests by poolId
   */
  async getRequestsByPoolId(poolId, limit = 50) {
    if (!this.redisClient) return [];

    try {
      const indexKey = `${this.indexPrefix}pool:${poolId}`;
      const requestIds = await this.redisClient.zrevrange(indexKey, 0, limit - 1);
      return await this.getRequestsByIds(requestIds);
    } catch (error) {
      logger.error('Failed to query requests by poolId:', error);
      return [];
    }
  }

  /**
   * Helper method to fetch multiple requests by IDs
   */
  async getRequestsByIds(requestIds) {
    if (!requestIds || requestIds.length === 0) return [];

    try {
      const keys = requestIds.map(id => `${this.keyPrefix}${id}`);
      const results = await this.redisClient.mget(keys);
      
      return results
        .filter(result => result !== null)
        .map(result => JSON.parse(result))
        .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
    } catch (error) {
      logger.error('Failed to fetch requests by IDs:', error);
      return [];
    }
  }

  /**
   * Get statistics
   */
  async getStatistics() {
    if (!this.redisClient) return null;

    try {
      const timelineKey = `${this.indexPrefix}timeline`;
      const totalRequests = await this.redisClient.zcard(timelineKey);
      
      // Get recent requests to calculate success rate
      const recentRequests = await this.getRecentRequests(100);
      const successfulRequests = recentRequests.filter(req => req.result.success).length;
      const successRate = recentRequests.length > 0 ? (successfulRequests / recentRequests.length * 100).toFixed(2) : 0;

      return {
        totalRequests,
        recentRequests: recentRequests.length,
        successRate: `${successRate}%`,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      logger.error('Failed to get statistics:', error);
      return null;
    }
  }
}

module.exports = new RequestHistoryService();
