# Secure Pool Server

A production-grade, highly secure Node.js server for blockchain pool management with enterprise-level security features.

## 🔒 Security Features

- **Encrypted Private Key Storage**: Private keys are encrypted using AES-256-GCM with PBKDF2 key derivation
- **Comprehensive Input Validation**: All inputs are validated and sanitized
- **Rate Limiting**: Multiple layers of rate limiting to prevent abuse
- **Brute Force Protection**: Exponential backoff for failed attempts
- **Security Headers**: Complete set of security headers (HSTS, CSP, etc.)
- **Request Sanitization**: XSS and injection attack prevention
- **Audit Logging**: Comprehensive security event logging
- **Health Monitoring**: Real-time system and security monitoring
- **Peer Connectivity Validation**: LibP2P-based peer reachability verification

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ 
- Redis (for rate limiting and caching)
- Docker (optional, for containerized deployment)

### Installation

1. **Clone and install dependencies:**
```bash
git clone <repository-url>
cd secure-pool-server
npm install
```

2. **Generate and encrypt your private key:**
```bash
# Generate a secure master password
node src/utils/keyManager.js generate-master-password

# Encrypt your private key
node src/utils/keyManager.js encrypt
```

3. **Configure environment variables:**
```bash
cp .env.example .env
# Edit .env with your configuration
```

4. **Start the server:**
```bash
# Development
npm run dev

# Production
npm start

# With PM2
npm run pm2:start
```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `NODE_ENV` | Environment (development/production) | Yes |
| `PORT` | Server port | No (default: 3000) |
| `ENCRYPTION_KEY` | 32-byte hex encryption key | Yes |
| `MASTER_PASSWORD` | Master password for key derivation | Yes |
| `ENCRYPTED_PRIVATE_KEY` | Base64 encrypted private key | Yes |
| `BASE_RPC_URL` | Base network RPC URL | Yes |
| `SKALE_RPC_URL` | Skale network RPC URL | Yes |
| `POOL_STORAGE_CONTRACT` | Contract address | Yes |
| `REDIS_URL` | Redis connection URL | No |

### Supported Networks

- **Base**: Mainnet Base network
- **Skale**: Skale Europa Hub network

## 📡 API Endpoints

### GET /
Returns unauthorized message for security.

**Response:**
```json
{
  "status": "err",
  "msg": "You are not authorized"
}
```

### POST /join
Adds a member to a blockchain pool after validating peer connectivity.

**Process:**
1. Validates input parameters
2. Checks peer connectivity through libp2p relay
3. Validates pool existence and capacity
4. Executes blockchain transaction

**Request:**
```json
{
  "peerId": "user_peer_id",
  "account": "0x********90********90********90********90",
  "chain": "base",
  "poolId": 123
}
```

**Response (Success):**
```json
{
  "status": "ok",
  "msg": "joined",
  "transactionHash": "0x..."
}
```

**Response (Error):**
```json
{
  "status": "err",
  "msg": "Error description"
}
```

### GET /health
Health check endpoint with service status.

**Response:**
```json
{
  "status": "ok",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "uptime": 3600,
  "environment": "production",
  "version": "1.0.0",
  "services": {
    "base": {
      "status": "healthy",
      "blockNumber": ********,
      "chainId": 8453
    },
    "skale": {
      "status": "healthy",
      "blockNumber": ********,
      "chainId": **********
    },
    "libp2p": {
      "status": "healthy",
      "peerId": "12D3KooW...",
      "connections": 5,
      "peers": 3
    }
  }
}
```

### GET /libp2p/status
LibP2P connectivity status (for debugging).

**Response:**
```json
{
  "status": "ok",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "libp2p": {
    "initialized": true,
    "peerId": "12D3KooW...",
    "connections": 5,
    "peers": 3,
    "addresses": ["/ip4/127.0.0.1/tcp/4001"],
    "health": {
      "status": "healthy"
    }
  }
}
```

## 🐳 Docker Deployment

### Development
```bash
docker-compose up --build
```

### Production
```bash
docker-compose -f docker-compose.yml up -d
```

## 🔐 Security Best Practices

### Private Key Management

1. **Never store private keys in plain text**
2. **Use the provided key manager utility**
3. **Store master passwords separately from encrypted keys**
4. **Rotate keys regularly**
5. **Use different keys for different environments**

### Environment Security

1. **Use environment variables for all secrets**
2. **Never commit .env files**
3. **Use secure secret management in production**
4. **Implement proper access controls**
5. **Monitor for security events**

### Network Security

1. **Use HTTPS in production**
2. **Implement proper firewall rules**
3. **Use VPN for administrative access**
4. **Monitor network traffic**
5. **Implement DDoS protection**

## 📊 Monitoring

The server includes comprehensive monitoring:

- **Request metrics**: Success/failure rates, response times
- **Transaction metrics**: Blockchain transaction success rates
- **Security metrics**: Rate limit hits, suspicious activity
- **System metrics**: Memory usage, CPU usage, uptime
- **Health checks**: Automated health monitoring

Access metrics via logs or implement custom monitoring endpoints.

## 🔧 Production Deployment

### Using PM2

```bash
# Install PM2 globally
npm install -g pm2

# Start with PM2
npm run pm2:start

# Monitor
pm2 monit

# View logs
pm2 logs
```

### Using Docker

```bash
# Build production image
docker build -t secure-pool-server .

# Run with docker-compose
docker-compose up -d
```

### Security Checklist

- [ ] Private keys encrypted and stored securely
- [ ] Environment variables configured
- [ ] Redis secured with password
- [ ] SSL certificates installed
- [ ] Firewall configured
- [ ] Monitoring set up
- [ ] Backup procedures in place
- [ ] Incident response plan ready

## 🛠 Development

### Running Tests
```bash
npm test
```

### Testing LibP2P Connectivity
```bash
# Test peer connectivity
node test-libp2p.js <peer-id>

# Example
node test-libp2p.js 12D3KooWExample123...
```

### Linting
```bash
npm run lint
```

### Security Audit
```bash
npm run security-audit
```

## 📝 Logging

Logs are structured and include:

- **Request logs**: All HTTP requests with timing
- **Security logs**: Security events and alerts
- **Transaction logs**: Blockchain transaction details
- **Error logs**: Application errors with stack traces
- **System logs**: Performance and health metrics

Log files are rotated daily and compressed automatically.

## 🚨 Incident Response

In case of security incidents:

1. **Check security logs** for suspicious activity
2. **Review rate limiting metrics** for abuse patterns
3. **Monitor transaction logs** for failed operations
4. **Check system metrics** for performance issues
5. **Implement additional security measures** if needed

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Implement changes with tests
4. Run security audit
5. Submit pull request

## 📄 License

MIT License - see LICENSE file for details.

## ⚠️ Disclaimer

This software handles cryptocurrency private keys and transactions. Use at your own risk. Always test thoroughly in development environments before production deployment.
