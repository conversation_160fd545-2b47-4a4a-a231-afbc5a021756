#!/bin/bash

# Development Upgrade Script
# Simpler version for development environments or when running as non-root

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
CURRENT_DIR="$(pwd)"
BACKUP_DIR="/tmp/pool-server-dev-backup-$(date +%Y%m%d_%H%M%S)"
LOG_FILE="/tmp/upgrade-dev-$(date +%Y%m%d_%H%M%S).log"

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1" | tee -a "$LOG_FILE"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

# Function to check if we're in the right directory
check_directory() {
    if [ ! -f "package.json" ] || [ ! -f "src/server.js" ]; then
        print_error "This doesn't appear to be the pool server directory"
        print_error "Please run this script from the pool server root directory"
        exit 1
    fi
    print_success "Directory check passed"
}

# Function to create backup
create_backup() {
    print_status "Creating backup of current installation..."
    
    mkdir -p "$BACKUP_DIR"
    
    # Backup current directory (excluding node_modules and logs)
    rsync -av --exclude='node_modules' --exclude='logs' --exclude='.git' \
          "$CURRENT_DIR/" "$BACKUP_DIR/" > /dev/null 2>&1
    
    print_success "Backup created: $BACKUP_DIR"
}

# Function to stop application (if running with PM2 or similar)
stop_application() {
    print_status "Stopping application..."
    
    # Try to stop with PM2 if available
    if command -v pm2 &> /dev/null; then
        pm2 stop pool-server 2>/dev/null || true
        pm2 stop libp2p-service 2>/dev/null || true
        print_success "Stopped PM2 processes"
    fi
    
    # Kill any running node processes for this app
    pkill -f "node.*server.js" 2>/dev/null || true
    pkill -f "libp2p-service" 2>/dev/null || true
    
    sleep 2
    print_success "Application stopped"
}

# Function to update code
update_code() {
    print_status "Updating application code..."
    
    # Check if it's a git repository
    if [ -d ".git" ]; then
        print_status "Pulling latest changes from git..."

        # Stash any local changes
        git stash push -m "Auto-stash before upgrade $(date)" 2>/dev/null || true

        # Pull latest changes
        git fetch origin

        # Determine the default branch (main or master)
        local default_branch=""
        if git show-ref --verify --quiet refs/remotes/origin/main; then
            default_branch="main"
        elif git show-ref --verify --quiet refs/remotes/origin/master; then
            default_branch="master"
        else
            # Fallback: try to detect from remote HEAD
            default_branch=$(git symbolic-ref refs/remotes/origin/HEAD 2>/dev/null | sed 's@^refs/remotes/origin/@@' || echo "master")
        fi

        print_status "Using branch: $default_branch"
        git reset --hard origin/$default_branch

        print_success "Git repository updated"
    else
        print_warning "Not a git repository. Please ensure you have the latest code."
        read -r -p "Press Enter when ready to continue..."
    fi
}

# Function to update dependencies
update_dependencies() {
    print_status "Updating Node.js dependencies..."
    
    # Remove node_modules and package-lock.json for clean install
    rm -rf node_modules package-lock.json 2>/dev/null || true
    
    # Install dependencies
    npm install
    
    print_success "Dependencies updated"
}

# Function to build libp2p service
build_libp2p() {
    if [ -d "libp2p-service" ] && command -v go &> /dev/null; then
        print_status "Building libp2p service..."
        cd libp2p-service
        go build -o libp2p-service main.go
        chmod +x libp2p-service
        cd ..
        print_success "LibP2P service built"
    else
        print_warning "Skipping libp2p build (Go not available or directory not found)"
    fi
}

# Function to clear cache
clear_cache() {
    print_status "Clearing application cache..."
    
    # Clear Redis if available and configured
    if [ -f ".env" ]; then
        local redis_password=$(grep "^REDIS_PASSWORD=" .env 2>/dev/null | cut -d'=' -f2 || echo "")
        
        if command -v redis-cli &> /dev/null; then
            if [ -n "$redis_password" ]; then
                redis-cli -a "$redis_password" FLUSHALL > /dev/null 2>&1 || true
            else
                redis-cli FLUSHALL > /dev/null 2>&1 || true
            fi
            print_success "Redis cache cleared"
        fi
    fi
    
    # Clear any temporary files
    rm -rf /tmp/pool-server-* 2>/dev/null || true
    
    print_success "Cache cleared"
}

# Function to start application
start_application() {
    print_status "Starting application..."
    
    # Start with PM2 if available
    if command -v pm2 &> /dev/null && [ -f "ecosystem.config.js" ]; then
        pm2 start ecosystem.config.js
        print_success "Started with PM2"
    else
        print_status "PM2 not available. You can start manually with:"
        echo "  npm start"
        echo "  or"
        echo "  node src/server.js"
    fi
}

# Function to verify upgrade
verify_upgrade() {
    print_status "Verifying upgrade..."
    
    # Check if package.json has expected dependencies
    if grep -q "winston" package.json && grep -q "redis" package.json; then
        print_success "Dependencies look correct"
    else
        print_warning "Some expected dependencies may be missing"
    fi
    
    # Check if new files exist
    if [ -f "src/services/requestHistory.js" ]; then
        print_success "New request history service found"
    else
        print_warning "Request history service not found"
    fi
    
    if [ -f "query-history.sh" ]; then
        print_success "Query history script found"
        chmod +x query-history.sh 2>/dev/null || true
    fi
    
    if [ -f "upgrade.sh" ]; then
        chmod +x upgrade.sh 2>/dev/null || true
    fi
    
    print_success "Verification completed"
}

# Function to show post-upgrade instructions
show_instructions() {
    echo
    print_success "🎉 Development upgrade completed!"
    echo
    echo "Next steps:"
    echo "1. Start the application:"
    echo "   npm start"
    echo "   or"
    echo "   pm2 start ecosystem.config.js"
    echo
    echo "2. Test the new history functionality:"
    echo "   ./query-history.sh stats"
    echo "   ./query-history.sh recent 5"
    echo
    echo "3. Monitor logs:"
    echo "   tail -f logs/combined-$(date +%Y-%m-%d).log"
    echo
    echo "Backup location: $BACKUP_DIR"
    echo "Log file: $LOG_FILE"
    echo
}

# Function to rollback
rollback() {
    print_error "Upgrade failed. Rolling back..."
    
    # Stop any running processes
    pkill -f "node.*server.js" 2>/dev/null || true
    pm2 stop all 2>/dev/null || true
    
    # Restore from backup
    if [ -d "$BACKUP_DIR" ]; then
        rsync -av --delete "$BACKUP_DIR/" "$CURRENT_DIR/" > /dev/null 2>&1
        print_success "Files restored from backup"
    fi
    
    print_success "Rollback completed"
    exit 1
}

# Main function
main() {
    echo
    echo "=============================================="
    echo -e "${BLUE}🚀 Pool Server Development Upgrade${NC}"
    echo "=============================================="
    echo
    echo "This script will upgrade the pool server application."
    echo "Current directory: $CURRENT_DIR"
    echo "Backup will be created at: $BACKUP_DIR"
    echo
    
    read -p "Do you want to continue? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "Upgrade cancelled."
        exit 0
    fi
    
    echo "Starting development upgrade..." | tee "$LOG_FILE"
    echo "Upgrade started at: $(date)" >> "$LOG_FILE"
    
    # Set trap for rollback on error
    trap rollback ERR
    
    # Run upgrade steps
    check_directory
    create_backup
    stop_application
    update_code
    update_dependencies
    build_libp2p
    clear_cache
    
    # Verify upgrade (disable trap for verification)
    trap - ERR
    verify_upgrade
    show_instructions
}

# Run main function
main "$@"
