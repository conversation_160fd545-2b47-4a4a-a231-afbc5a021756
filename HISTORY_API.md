# Join Request History API

This document describes the Redis-based join request history functionality that stores and allows querying of all join requests made to the pool server.

## Overview

The system automatically stores every join request along with its result, timestamp, and client information in Redis. This provides a complete audit trail of all pool join attempts.

## Storage Details

- **Storage**: Redis with 30-day TTL (automatic cleanup)
- **Indexing**: Multiple indexes for efficient querying by peerId, account, chain, poolId
- **Data**: Complete request parameters, results, timestamps, and client IPs

## API Endpoints

All history endpoints are **localhost-only** for security. They can only be accessed from the server itself using curl or similar tools.

### Base URL
```
http://localhost:3000/local/history/
```

### Available Endpoints

#### 1. Recent Requests
```bash
GET /local/history/recent?limit=50
```
Returns the most recent join requests.

**Example:**
```bash
curl "http://localhost:3000/local/history/recent?limit=10"
```

#### 2. Query by Peer ID
```bash
GET /local/history/peer/{peerId}?limit=50
```
Returns all requests from a specific peer ID.

**Example:**
```bash
curl "http://localhost:3000/local/history/peer/12D3KooWABC123..."
```

#### 3. Query by Account
```bash
GET /local/history/account/{account}?limit=50
```
Returns all requests from a specific Ethereum account.

**Example:**
```bash
curl "http://localhost:3000/local/history/account/0x1234567890abcdef..."
```

#### 4. Query by Chain
```bash
GET /local/history/chain/{chain}?limit=50
```
Returns all requests for a specific blockchain (base/skale).

**Example:**
```bash
curl "http://localhost:3000/local/history/chain/base"
```

#### 5. Query by Pool ID
```bash
GET /local/history/pool/{poolId}?limit=50
```
Returns all requests for a specific pool.

**Example:**
```bash
curl "http://localhost:3000/local/history/pool/1"
```

#### 6. Statistics
```bash
GET /local/history/stats
```
Returns overall statistics about join requests.

**Example:**
```bash
curl "http://localhost:3000/local/history/stats"
```

## Response Format

All endpoints return JSON responses in this format:

### Success Response
```json
{
  "status": "ok",
  "count": 5,
  "requests": [
    {
      "requestId": "1703123456789_abc123def",
      "timestamp": "2023-12-21T10:30:45.123Z",
      "ip": "*************",
      "request": {
        "peerId": "12D3KooWABC123...",
        "account": "0x1234567890abcdef...",
        "chain": "base",
        "poolId": "1"
      },
      "result": {
        "success": true,
        "transactionHash": "0xabc123def456...",
        "error": null,
        "statusCode": 200
      }
    }
  ]
}
```

### Error Response
```json
{
  "status": "err",
  "msg": "Error description"
}
```

### Statistics Response
```json
{
  "status": "ok",
  "statistics": {
    "totalRequests": 1250,
    "recentRequests": 100,
    "successRate": "85.50%",
    "timestamp": "2023-12-21T10:30:45.123Z"
  }
}
```

## Helper Scripts

### query-history.sh
A convenient script for querying the history API:

```bash
# Make executable
chmod +x query-history.sh

# Usage examples
./query-history.sh recent 10
./query-history.sh peer 12D3KooWABC123...
./query-history.sh account 0x1234567890abcdef...
./query-history.sh chain base
./query-history.sh pool 1
./query-history.sh stats
```

### test-history.sh
A test script to verify the history functionality:

```bash
# Make executable
chmod +x test-history.sh

# Run tests
./test-history.sh
```

## Security Features

1. **Localhost Only**: All history endpoints are restricted to localhost access
2. **No External Access**: Endpoints return 403 Forbidden for non-localhost requests
3. **Privacy Protection**: Peer IDs and accounts are partially masked in logs
4. **Automatic Cleanup**: Data expires after 30 days
5. **Access Logging**: All access attempts are logged for security monitoring

## Data Retention

- **TTL**: 30 days automatic expiration
- **Storage**: Redis memory-based storage
- **Cleanup**: Automatic, no manual intervention required
- **Backup**: Not included (logs provide permanent record if needed)

## Monitoring

The system logs all history operations:
- Request storage events
- Query operations
- Access denied attempts
- Redis connection issues

Check the application logs for detailed information:
```bash
tail -f logs/combined-$(date +%Y-%m-%d).log | grep -i history
```

## Troubleshooting

### Common Issues

1. **403 Forbidden**: Ensure you're accessing from localhost
2. **Empty Results**: Check if Redis is running and connected
3. **Connection Errors**: Verify Redis configuration and password

### Debugging Commands

```bash
# Check Redis connection
redis-cli -a YOUR_REDIS_PASSWORD ping

# Check if data exists
redis-cli -a YOUR_REDIS_PASSWORD keys "join_history:*" | head -5

# Check indexes
redis-cli -a YOUR_REDIS_PASSWORD keys "join_index:*" | head -5
```

## Integration

The history system is automatically integrated with the join endpoint. No additional configuration is required beyond having Redis properly configured.

Every join request (successful or failed) is automatically stored with complete details for later analysis and auditing.
