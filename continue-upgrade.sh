#!/bin/bash

# Continue Upgrade Script
# This script helps continue an upgrade that failed partway through

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
INSTALL_DIR="/opt/mainnet"
SERVICE_NAME="pool-server"
LIBP2P_SERVICE_NAME="libp2p-service"
LOG_FILE="/tmp/continue-upgrade-$(date +%Y%m%d_%H%M%S).log"

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1" | tee -a "$LOG_FILE"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

# Function to check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        print_error "This script must be run as root"
        exit 1
    fi
}

# Function to fix git branch issue and update code
fix_git_and_update() {
    print_status "Fixing git branch issue and updating code..."
    
    cd "$INSTALL_DIR"
    
    # Determine the correct branch
    local default_branch=""
    if git show-ref --verify --quiet refs/remotes/origin/main; then
        default_branch="main"
    elif git show-ref --verify --quiet refs/remotes/origin/master; then
        default_branch="master"
    else
        # Fallback: try to detect from remote HEAD
        default_branch=$(git symbolic-ref refs/remotes/origin/HEAD 2>/dev/null | sed 's@^refs/remotes/origin/@@' || echo "master")
    fi
    
    print_status "Using branch: $default_branch"
    
    # Update to the correct branch
    git reset --hard origin/$default_branch
    print_success "Git repository updated to $default_branch"
    
    # Update dependencies
    print_status "Updating Node.js dependencies..."
    npm ci --production
    print_success "Dependencies updated"
    
    # Rebuild libp2p service if needed
    if [ -d "libp2p-service" ] && command -v go &> /dev/null; then
        print_status "Rebuilding libp2p service..."
        cd libp2p-service
        go build -o libp2p-service main.go
        chmod +x libp2p-service
        cd ..
        print_success "LibP2P service rebuilt"
    fi
}

# Function to clear cache
clear_cache() {
    print_status "Clearing application cache..."
    
    # Clear Redis if available and configured
    if systemctl is-active --quiet redis-server; then
        local redis_password=""
        if [ -f "$INSTALL_DIR/.env" ]; then
            redis_password=$(grep "^REDIS_PASSWORD=" "$INSTALL_DIR/.env" 2>/dev/null | cut -d'=' -f2)
        fi
        
        if [ -n "$redis_password" ]; then
            redis-cli -a "$redis_password" FLUSHALL > /dev/null 2>&1 || true
        else
            redis-cli FLUSHALL > /dev/null 2>&1 || true
        fi
        print_success "Redis cache cleared"
    fi
}

# Function to start services
start_services() {
    print_status "Starting services..."
    
    # Reload systemd in case service files changed
    systemctl daemon-reload
    
    # Start libp2p service first
    if [ -f "/etc/systemd/system/$LIBP2P_SERVICE_NAME.service" ]; then
        systemctl start "$LIBP2P_SERVICE_NAME"
        print_success "Started $LIBP2P_SERVICE_NAME"
        sleep 2
    fi
    
    # Start main service
    systemctl start "$SERVICE_NAME"
    print_success "Started $SERVICE_NAME"
    
    # Wait for services to be ready
    sleep 5
}

# Function to verify upgrade
verify_upgrade() {
    print_status "Verifying upgrade..."
    
    # Check if services are running
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        print_success "Main service is running"
    else
        print_error "Main service failed to start"
        return 1
    fi
    
    if [ -f "/etc/systemd/system/$LIBP2P_SERVICE_NAME.service" ]; then
        if systemctl is-active --quiet "$LIBP2P_SERVICE_NAME"; then
            print_success "LibP2P service is running"
        else
            print_warning "LibP2P service not running"
        fi
    fi
    
    # Test health endpoint
    local port=$(grep "^PORT=" "$INSTALL_DIR/.env" 2>/dev/null | cut -d'=' -f2 || echo "3000")
    local health_url="http://localhost:$port/health"
    
    print_status "Testing health endpoint..."
    local max_attempts=10
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "$health_url" > /dev/null 2>&1; then
            print_success "Health check passed"
            break
        else
            print_status "Attempt $attempt/$max_attempts - waiting for service..."
            sleep 3
            ((attempt++))
        fi
    done
    
    if [ $attempt -gt $max_attempts ]; then
        print_error "Health check failed after $max_attempts attempts"
        return 1
    fi
    
    # Test history endpoints
    if curl -s "http://localhost:$port/local/history/stats" > /dev/null 2>&1; then
        print_success "History API is working"
    else
        print_warning "History API test failed (may be normal if no data yet)"
    fi
    
    print_success "Upgrade verification completed"
}

# Main function
main() {
    echo
    echo "=============================================="
    echo -e "${BLUE}🔧 Continue Pool Server Upgrade${NC}"
    echo "=============================================="
    echo
    echo "This script will continue the upgrade from where it failed."
    echo "It will fix the git branch issue and complete the upgrade."
    echo
    echo "Log file: $LOG_FILE"
    echo
    
    read -r -p "Do you want to continue? (y/N): " -n 1
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "Continue upgrade cancelled."
        exit 0
    fi
    
    echo "Continuing upgrade..." | tee "$LOG_FILE"
    echo "Continue upgrade started at: $(date)" >> "$LOG_FILE"
    
    # Run upgrade steps
    check_root
    fix_git_and_update
    clear_cache
    start_services
    verify_upgrade
    
    echo
    print_success "🎉 Upgrade completed successfully!"
    echo
    echo "Services status:"
    systemctl status "$SERVICE_NAME" --no-pager -l
    echo
    echo "Test the new history functionality:"
    echo "  curl http://localhost:3000/local/history/stats"
    echo "  ./query-history.sh stats"
    echo
    echo "Log file: $LOG_FILE"
    echo
}

# Run main function
main "$@"
