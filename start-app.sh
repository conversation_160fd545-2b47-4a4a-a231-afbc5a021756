#!/bin/bash

# Quick script to start the application manually

echo "🔍 Diagnosing and Starting Application"
echo "====================================="

INSTALL_DIR="/opt/mainnet"

echo "1. Checking installation directory..."
if [[ -d "$INSTALL_DIR" ]]; then
    echo "✅ Installation directory exists"
    cd "$INSTALL_DIR"
    
    echo "Files in $INSTALL_DIR:"
    ls -la
    
    echo ""
    echo "2. Checking required files..."
    
    if [[ -f "src/server.js" ]]; then
        echo "✅ Server file exists"
    else
        echo "❌ Server file missing"
        exit 1
    fi
    
    if [[ -f ".env" ]]; then
        echo "✅ Environment file exists"
        echo "Environment variables:"
        grep -E "^(PORT|HOST|NODE_ENV)" .env
    else
        echo "❌ Environment file missing"
        exit 1
    fi
    
    if [[ -f "ecosystem.config.js" ]]; then
        echo "✅ PM2 config exists"
    else
        echo "❌ PM2 config missing"
    fi
    
    if [[ -d "node_modules" ]]; then
        echo "✅ Dependencies installed"
    else
        echo "❌ Dependencies missing - installing..."
        npm install --production
    fi
    
    echo ""
    echo "3. Testing manual startup..."
    
    # Kill any existing processes
    pkill -f "node.*server.js" 2>/dev/null || true
    
    # Test startup with timeout
    echo "Starting application manually..."
    timeout 15s node src/server.js &
    APP_PID=$!
    
    sleep 5
    
    if kill -0 $APP_PID 2>/dev/null; then
        echo "✅ Application started successfully"
        
        # Test health endpoint
        sleep 2
        if curl -f http://localhost:3001/health > /dev/null 2>&1; then
            echo "✅ Health check passed"
            echo "Response:"
            curl -s http://localhost:3001/health
        else
            echo "❌ Health check failed"
        fi
        
        # Kill the test process
        kill $APP_PID 2>/dev/null || true
        
        echo ""
        echo "4. Starting with PM2..."
        
        # Clean PM2
        pm2 kill 2>/dev/null || true
        
        # Start with PM2
        if pm2 start ecosystem.config.js --env production; then
            echo "✅ Started with PM2"
            pm2 save
            
            echo ""
            echo "PM2 Status:"
            pm2 status
            
            echo ""
            echo "Testing health endpoint again..."
            sleep 3
            if curl -f http://localhost:3001/health > /dev/null 2>&1; then
                echo "✅ Application running and healthy"
                echo "Response:"
                curl -s http://localhost:3001/health
            else
                echo "❌ Application not responding"
                echo "PM2 logs:"
                pm2 logs --lines 10
            fi
        else
            echo "❌ PM2 start failed"
            echo "Trying direct startup..."
            nohup node src/server.js > logs/server.log 2>&1 &
            sleep 3
            
            if pgrep -f "node.*server.js" > /dev/null; then
                echo "✅ Application started with nohup"
            else
                echo "❌ All startup methods failed"
                echo "Checking logs..."
                if [[ -f "logs/server.log" ]]; then
                    echo "Server log:"
                    tail -20 logs/server.log
                fi
            fi
        fi
        
    else
        echo "❌ Application failed to start manually"
        echo "Checking for errors..."
        
        # Check if there are any log files
        if [[ -d "logs" ]]; then
            echo "Recent logs:"
            find logs -name "*.log" -exec tail -10 {} \;
        fi
        
        # Try to see what went wrong
        echo ""
        echo "Trying to get error output..."
        node src/server.js 2>&1 | head -20
    fi
    
else
    echo "❌ Installation directory $INSTALL_DIR does not exist"
    echo "Please run the installation script first"
    exit 1
fi

echo ""
echo "🎯 Diagnosis Complete!"
echo ""
echo "Current status:"
echo "   • Application process: $(pgrep -f "node.*server.js" > /dev/null && echo "✅ Running" || echo "❌ Not running")"
echo "   • Port 3001: $(netstat -tlnp | grep -q ":3001 " && echo "✅ In use" || echo "❌ Not in use")"
echo "   • Health check: $(curl -f http://localhost:3001/health > /dev/null 2>&1 && echo "✅ Passing" || echo "❌ Failing")"
echo ""
echo "📋 Next steps:"
echo "   • Check status: pm2 status"
echo "   • View logs: pm2 logs"
echo "   • Test health: curl http://localhost:3001/health"
