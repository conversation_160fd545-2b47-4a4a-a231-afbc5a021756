#!/usr/bin/env node

/**
 * Test script for wallet reset functionality
 * This script tests the new /wallet/reset endpoint
 */

const http = require('http');

// Configuration
const HOST = 'localhost';
const PORT = process.env.PORT || 3000;

function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => {
        body += chunk;
      });
      res.on('end', () => {
        try {
          const parsed = JSON.parse(body);
          resolve({ status: res.statusCode, data: parsed });
        } catch (error) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

async function testWalletReset() {
  console.log('🔧 Testing Wallet Reset Endpoint');
  console.log('================================\n');

  try {
    // Test the wallet reset endpoint
    console.log('Testing POST /wallet/reset...');
    
    const resetOptions = {
      hostname: HOST,
      port: PORT,
      path: '/wallet/reset',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'WalletResetTest/1.0'
      }
    };

    const resetResult = await makeRequest(resetOptions);
    
    console.log(`Status: ${resetResult.status}`);
    console.log('Response:', JSON.stringify(resetResult.data, null, 2));
    
    if (resetResult.status === 200) {
      console.log('\n✅ Wallet reset successful!');
      
      if (resetResult.data.walletAddress) {
        console.log(`🔑 Wallet Address: ${resetResult.data.walletAddress}`);
      }
      
      // Test health check to verify wallet is working
      console.log('\nTesting health check after reset...');
      
      const healthOptions = {
        hostname: HOST,
        port: PORT,
        path: '/health',
        method: 'GET'
      };

      const healthResult = await makeRequest(healthOptions);
      console.log(`Health Status: ${healthResult.status}`);
      
      if (healthResult.status === 200) {
        console.log('✅ Health check passed after wallet reset');
      } else {
        console.log('❌ Health check failed after wallet reset');
        console.log('Health Response:', JSON.stringify(healthResult.data, null, 2));
      }
      
    } else {
      console.log('\n❌ Wallet reset failed');
      console.log('Error details:', resetResult.data);
    }

  } catch (error) {
    console.error('\n❌ Test failed with error:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Make sure the server is running:');
      console.log('   npm start');
      console.log(`   Server should be accessible at http://${HOST}:${PORT}`);
    }
  }
}

// Run the test
if (require.main === module) {
  testWalletReset().catch(console.error);
}

module.exports = { testWalletReset };
