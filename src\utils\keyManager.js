#!/usr/bin/env node

/**
 * Secure Key Management Utility
 * 
 * This utility helps you securely encrypt your private key for storage.
 * Run this script to encrypt your private key before deploying to production.
 * 
 * Usage:
 *   node src/utils/keyManager.js encrypt <private_key>
 *   node src/utils/keyManager.js decrypt <encrypted_key>
 *   node src/utils/keyManager.js generate-master-password
 */

const crypto = require('crypto');
const readline = require('readline');
const security = require('../config/security');

class KeyManager {
  constructor() {
    this.algorithm = 'aes-256-gcm';
    this.keyLength = 32;
    this.ivLength = 16;
    this.tagLength = 16;
    this.iterations = 100000;
  }

  async promptForPassword(prompt) {
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    return new Promise((resolve) => {
      rl.question(prompt, (answer) => {
        rl.close();
        resolve(answer);
      });
    });
  }

  async promptForSecretPassword(prompt) {
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    // Hide input for password
    rl.stdoutMuted = true;
    rl._writeToOutput = function _writeToOutput(stringToWrite) {
      if (rl.stdoutMuted) {
        rl.output.write('*');
      } else {
        rl.output.write(stringToWrite);
      }
    };

    return new Promise((resolve) => {
      rl.question(prompt, (answer) => {
        rl.close();
        console.log(''); // New line after hidden input
        resolve(answer);
      });
    });
  }

  deriveKey(password, salt) {
    return crypto.pbkdf2Sync(password, salt, this.iterations, this.keyLength, 'sha512');
  }

  encrypt(plaintext, password) {
    try {
      const salt = crypto.randomBytes(32);
      const key = this.deriveKey(password, salt);
      const iv = crypto.randomBytes(this.ivLength);
      
      const cipher = crypto.createCipheriv(this.algorithm, key, iv);
      
      let encrypted = cipher.update(plaintext, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      
      const tag = cipher.getAuthTag();
      
      // Combine salt, IV, tag, and encrypted data
      const combined = Buffer.concat([
        salt,
        iv, 
        tag, 
        Buffer.from(encrypted, 'hex')
      ]);
      
      return combined.toString('base64');
    } catch (error) {
      throw new Error('Encryption failed: ' + error.message);
    }
  }

  decrypt(encryptedData, password) {
    try {
      const combined = Buffer.from(encryptedData, 'base64');
      
      const salt = combined.slice(0, 32);
      const iv = combined.slice(32, 32 + this.ivLength);
      const tag = combined.slice(32 + this.ivLength, 32 + this.ivLength + this.tagLength);
      const encrypted = combined.slice(32 + this.ivLength + this.tagLength);
      
      const key = this.deriveKey(password, salt);
      
      const decipher = crypto.createDecipheriv(this.algorithm, key, iv);
      decipher.setAuthTag(tag);
      
      let decrypted = decipher.update(encrypted, null, 'utf8');
      decrypted += decipher.final('utf8');
      
      return decrypted;
    } catch (error) {
      throw new Error('Decryption failed: ' + error.message);
    }
  }

  validatePrivateKey(privateKey) {
    if (!privateKey || typeof privateKey !== 'string') {
      throw new Error('Private key must be a string');
    }
    
    if (!privateKey.startsWith('0x')) {
      throw new Error('Private key must start with 0x');
    }
    
    if (privateKey.length !== 66) {
      throw new Error('Private key must be 66 characters long (including 0x prefix)');
    }
    
    if (!/^0x[a-fA-F0-9]{64}$/.test(privateKey)) {
      throw new Error('Private key contains invalid characters');
    }
    
    return true;
  }

  generateMasterPassword() {
    // Generate a cryptographically secure random password
    const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
    let password = '';
    
    for (let i = 0; i < 32; i++) {
      const randomIndex = crypto.randomInt(0, charset.length);
      password += charset[randomIndex];
    }
    
    return password;
  }

  async encryptPrivateKey() {
    try {
      console.log('🔐 Secure Private Key Encryption Utility');
      console.log('=========================================\n');
      
      const privateKey = await this.promptForPassword('Enter your private key (0x...): ');
      
      // Validate private key
      this.validatePrivateKey(privateKey);
      
      const masterPassword = await this.promptForSecretPassword('Enter master password: ');
      
      if (masterPassword.length < 16) {
        throw new Error('Master password must be at least 16 characters long');
      }
      
      const confirmPassword = await this.promptForSecretPassword('Confirm master password: ');
      
      if (masterPassword !== confirmPassword) {
        throw new Error('Passwords do not match');
      }
      
      const encryptedKey = this.encrypt(privateKey, masterPassword);
      
      console.log('\n✅ Private key encrypted successfully!');
      console.log('\n📋 Add this to your .env file:');
      console.log(`ENCRYPTED_PRIVATE_KEY=${encryptedKey}`);
      console.log(`MASTER_PASSWORD=${masterPassword}`);
      
      console.log('\n⚠️  SECURITY WARNINGS:');
      console.log('1. Store the MASTER_PASSWORD securely and separately from the encrypted key');
      console.log('2. Never commit the master password to version control');
      console.log('3. Use environment variables or secure secret management in production');
      console.log('4. Consider using different master passwords for different environments');
      
    } catch (error) {
      console.error('\n❌ Error:', error.message);
      process.exit(1);
    }
  }

  async decryptPrivateKey() {
    try {
      console.log('🔓 Private Key Decryption Utility');
      console.log('=================================\n');
      
      const encryptedKey = await this.promptForPassword('Enter encrypted private key: ');
      const masterPassword = await this.promptForSecretPassword('Enter master password: ');
      
      const decryptedKey = this.decrypt(encryptedKey, masterPassword);
      
      console.log('\n✅ Private key decrypted successfully!');
      console.log(`\n🔑 Decrypted private key: ${decryptedKey}`);
      
      console.log('\n⚠️  WARNING: Handle this private key with extreme care!');
      
    } catch (error) {
      console.error('\n❌ Error:', error.message);
      process.exit(1);
    }
  }

  generateMasterPasswordCommand() {
    console.log('🎲 Master Password Generator');
    console.log('============================\n');
    
    const password = this.generateMasterPassword();
    
    console.log(`Generated master password: ${password}`);
    console.log('\n⚠️  IMPORTANT:');
    console.log('1. Store this password securely');
    console.log('2. Never share or commit this password');
    console.log('3. Use this as your MASTER_PASSWORD environment variable');
  }
}

// CLI Interface
async function main() {
  const keyManager = new KeyManager();
  const command = process.argv[2];
  
  switch (command) {
    case 'encrypt':
      await keyManager.encryptPrivateKey();
      break;
      
    case 'decrypt':
      await keyManager.decryptPrivateKey();
      break;
      
    case 'generate-master-password':
      keyManager.generateMasterPasswordCommand();
      break;
      
    default:
      console.log('🔐 Secure Key Management Utility');
      console.log('================================\n');
      console.log('Usage:');
      console.log('  node src/utils/keyManager.js encrypt');
      console.log('  node src/utils/keyManager.js decrypt');
      console.log('  node src/utils/keyManager.js generate-master-password');
      console.log('\nCommands:');
      console.log('  encrypt                 - Encrypt a private key for secure storage');
      console.log('  decrypt                 - Decrypt an encrypted private key');
      console.log('  generate-master-password - Generate a secure master password');
      break;
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = KeyManager;
