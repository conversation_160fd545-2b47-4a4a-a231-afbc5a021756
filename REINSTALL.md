# 🔄 Reinstallation & Cleanup Guide

The installation script now includes comprehensive cleanup functionality for safe re-runs and updates.

## 🧹 **Automatic Cleanup Features**

### **Detection & Confirmation**
When you re-run `./install.sh`, it automatically detects:
- ✅ Existing systemd service
- ✅ Existing user account
- ✅ Existing installation directory
- ✅ Existing nginx configuration
- ✅ Running PM2 processes

### **Interactive Cleanup**
```bash
⚠️  Previous installation detected. This will:
   • Stop and remove existing services
   • Clean up PM2 processes
   • Remove existing installation directory
   • Update nginx configuration
   • Preserve SSL certificates

Do you want to continue with cleanup? (y/N):
```

## 🔒 **What Gets Cleaned Up**

### **Services & Processes**
- **Systemd Service**: Stops, disables, and removes service file
- **PM2 Processes**: Kills all PM2 processes for mainnet user
- **PM2 Data**: Removes PM2 configuration and process data
- **Nginx**: Temporarily stops nginx, removes old configuration

### **Files & Directories**
- **Installation Directory**: Completely removes `/opt/mainnet/`
- **Service Configuration**: Removes systemd service file
- **Nginx Configuration**: Removes site configuration
- **Fail2ban Rules**: Removes custom jail configuration
- **User Limits**: Cleans up resource limit configurations

### **Data Preservation**
- **SSL Certificates**: ✅ Preserved (expensive to regenerate)
- **Environment Backup**: ✅ Creates timestamped backup of `.env`
- **Log Backup**: ✅ Creates timestamped backup of logs
- **User Account**: ✅ Preserved (maintains UID/GID consistency)

## 💾 **Backup & Restoration**

### **Automatic Backups**
During cleanup, the script automatically creates:
```bash
/tmp/.env.backup.20240717_151234        # Environment configuration
/tmp/mainnet_logs_backup_20240717_151234/  # Application logs
```

### **Environment Restoration**
After creating new configuration, you can restore from backup:
```bash
Found environment backup: /tmp/.env.backup.20240717_151234
This backup contains your previous configuration including encrypted private key.

Do you want to restore from backup instead of creating new environment? (y/N):
```

**Benefits of Restoration:**
- ✅ Keeps your existing encrypted private key
- ✅ Preserves all configuration settings
- ✅ Maintains domain and SSL configuration
- ✅ Avoids re-entering sensitive information

## 🔄 **Reinstallation Scenarios**

### **1. Update Installation**
```bash
# Pull latest code
git pull origin main

# Re-run installation (will cleanup and update)
sudo ./install.sh
```

### **2. Fix Broken Installation**
```bash
# If something went wrong, just re-run
sudo ./install.sh

# Script will detect issues and clean up automatically
```

### **3. Change Configuration**
```bash
# Re-run to change domain, private key, or other settings
sudo ./install.sh

# Choose whether to restore previous config or create new
```

### **4. Complete Fresh Install**
```bash
# For completely fresh start, manually remove backups first
sudo rm -f /tmp/.env.backup.*
sudo rm -rf /tmp/mainnet_logs_backup_*

# Then run installation
sudo ./install.sh
```

## 🛡️ **Safety Features**

### **Confirmation Required**
- **Interactive Prompts**: Always asks before destructive actions
- **Backup Creation**: Automatically backs up important data
- **Graceful Shutdown**: Properly stops services before cleanup

### **Error Handling**
- **Partial Cleanup**: Continues even if some cleanup steps fail
- **Service Recovery**: Restarts nginx and other services after cleanup
- **Log Preservation**: Maintains installation logs for troubleshooting

### **Data Protection**
- **SSL Certificates**: Never removes expensive-to-regenerate certificates
- **User Data**: Preserves user account to maintain file ownership
- **Configuration**: Backs up before removal with restore option

## 📋 **Cleanup Process Details**

### **Step-by-Step Cleanup**
1. **Service Shutdown**
   ```bash
   systemctl stop mainnet-pool-server
   systemctl disable mainnet-pool-server
   rm /etc/systemd/system/mainnet-pool-server.service
   systemctl daemon-reload
   ```

2. **PM2 Cleanup**
   ```bash
   sudo -u mainnet pm2 kill
   sudo -u mainnet pm2 delete all
   rm -rf /opt/mainnet/.pm2
   ```

3. **File Backup & Removal**
   ```bash
   cp /opt/mainnet/.env /tmp/.env.backup.$(date +%Y%m%d_%H%M%S)
   cp -r /opt/mainnet/logs/ /tmp/mainnet_logs_backup_$(date +%Y%m%d_%H%M%S)/
   rm -rf /opt/mainnet/
   ```

4. **Configuration Cleanup**
   ```bash
   rm /etc/nginx/sites-available/mainnet-pool-server
   rm /etc/nginx/sites-enabled/mainnet-pool-server
   rm /etc/fail2ban/jail.d/mainnet-pool-server.conf
   ```

## 🔧 **Manual Cleanup (If Needed)**

If you need to manually clean up:

```bash
# Stop all services
sudo systemctl stop mainnet-pool-server nginx redis-server
sudo systemctl disable mainnet-pool-server

# Clean up PM2
sudo -u mainnet pm2 kill
sudo -u mainnet pm2 delete all

# Remove files
sudo rm -rf /opt/mainnet/
sudo rm -f /etc/systemd/system/mainnet-pool-server.service
sudo rm -f /etc/nginx/sites-available/mainnet-pool-server
sudo rm -f /etc/nginx/sites-enabled/mainnet-pool-server
sudo rm -f /etc/fail2ban/jail.d/mainnet-pool-server.conf

# Reload configurations
sudo systemctl daemon-reload
sudo systemctl restart fail2ban nginx
```

## ✅ **Best Practices**

### **Before Reinstalling**
1. **Backup Important Data**: Script does this automatically
2. **Note Current Configuration**: Domain, settings, etc.
3. **Check Service Status**: `systemctl status mainnet-pool-server`

### **During Reinstallation**
1. **Review Cleanup Prompts**: Understand what will be removed
2. **Choose Backup Restoration**: Use previous config if appropriate
3. **Monitor Progress**: Watch for any errors in the process

### **After Reinstallation**
1. **Verify Services**: Check all services are running
2. **Test Functionality**: Run health checks
3. **Clean Up Backups**: Remove old backups if no longer needed

## 🚨 **Troubleshooting**

### **If Cleanup Fails**
- **Check Logs**: Review `/var/log/secure-pool-server-install.log`
- **Manual Cleanup**: Use manual cleanup commands above
- **Service Issues**: `systemctl reset-failed mainnet-pool-server`

### **If Restoration Fails**
- **Check Backup Files**: Verify backups exist in `/tmp/`
- **Manual Restoration**: Copy backup files manually
- **Permissions**: Ensure correct ownership after restoration

The enhanced installation script now provides enterprise-grade reinstallation capabilities with comprehensive cleanup, backup, and restoration features.
