const crypto = require('crypto');
const NodeRSA = require('node-rsa');
const bcrypt = require('bcrypt');
const Joi = require('joi');

class SecurityManager {
  constructor() {
    this.algorithm = 'aes-256-gcm';
    this.keyLength = 32;
    this.ivLength = 16;
    this.tagLength = 16;
    this.saltLength = 32;
    this.iterations = 100000;
    
    // Validate environment variables on initialization
    this.validateEnvironment();
    
    // Initialize encryption key from environment
    this.initializeEncryptionKey();
  }

  validateEnvironment() {
    const schema = Joi.object({
      NODE_ENV: Joi.string().valid('development', 'production', 'test').required(),
      ENCRYPTION_KEY: Joi.string().length(64).required(), // 32 bytes in hex
      MASTER_PASSWORD: Joi.string().min(16).required(),
      JWT_SECRET: Joi.string().min(32).required(),
      ENCRYPTED_PRIVATE_KEY: Joi.string().required(),
      PORT: Joi.number().port().default(3000),
      HOST: Joi.string().default('0.0.0.0')
    });

    // Only validate the specific environment variables we care about
    const envToValidate = {
      NODE_ENV: process.env.NODE_ENV,
      ENCRYPTION_KEY: process.env.ENCRYPTION_KEY,
      MASTER_PASSWORD: process.env.MASTER_PASSWORD,
      JWT_SECRET: process.env.JWT_SECRET,
      ENCRYPTED_PRIVATE_KEY: process.env.ENCRYPTED_PRIVATE_KEY,
      PORT: process.env.PORT,
      HOST: process.env.HOST
    };

    const { error } = schema.validate(envToValidate);
    if (error) {
      throw new Error(`Environment validation failed: ${error.details[0].message}`);
    }
  }

  initializeEncryptionKey() {
    try {
      // Derive key from master password and fixed salt for consistency
      const salt = crypto.createHash('sha256').update('POOL_SERVER_SALT').digest();
      this.masterKey = crypto.pbkdf2Sync(
        process.env.MASTER_PASSWORD,
        salt,
        this.iterations,
        this.keyLength,
        'sha512'
      );
    } catch (error) {
      throw new Error('Failed to initialize encryption key');
    }
  }

  /**
   * Encrypts sensitive data using AES-256-GCM
   * @param {string} plaintext - Data to encrypt
   * @returns {string} - Base64 encoded encrypted data with IV and tag
   */
  encrypt(plaintext) {
    try {
      const iv = crypto.randomBytes(this.ivLength);
      const cipher = crypto.createCipheriv(this.algorithm, this.masterKey, iv);

      let encrypted = cipher.update(plaintext, 'utf8', 'hex');
      encrypted += cipher.final('hex');

      const tag = cipher.getAuthTag();

      // Combine IV, tag, and encrypted data
      const combined = Buffer.concat([iv, tag, Buffer.from(encrypted, 'hex')]);
      return combined.toString('base64');
    } catch (error) {
      throw new Error('Encryption failed');
    }
  }

  /**
   * Decrypts data encrypted with encrypt method
   * @param {string} encryptedData - Base64 encoded encrypted data
   * @returns {string} - Decrypted plaintext
   */
  decrypt(encryptedData) {
    try {
      const combined = Buffer.from(encryptedData, 'base64');

      let offset = 0;
      let key;

      // Check if the blob contains a 32-byte salt prefix (keyManager format)
      // vs install.sh format which has no salt prefix
      if (combined.length >= 32 + this.ivLength + this.tagLength) {
        // Try keyManager format first (salt + iv + tag + ciphertext)
        try {
          const salt = combined.slice(0, 32);
          offset = 32;
          key = crypto.pbkdf2Sync(
            process.env.MASTER_PASSWORD,
            salt,
            this.iterations,
            this.keyLength,
            'sha512'
          );
        } catch (saltError) {
          // Fall back to install.sh format if salt-based decryption fails
          offset = 0;
          key = this.masterKey;
        }
      } else {
        // install.sh format (iv + tag + ciphertext, no salt)
        offset = 0;
        key = this.masterKey;
      }

      const iv = combined.slice(offset, offset + this.ivLength);
      offset += this.ivLength;

      const tag = combined.slice(offset, offset + this.tagLength);
      offset += this.tagLength;

      const encrypted = combined.slice(offset);

      const decipher = crypto.createDecipheriv(this.algorithm, key, iv);
      decipher.setAuthTag(tag);

      const plaintext = Buffer.concat([
        decipher.update(encrypted),
        decipher.final()
      ]).toString('utf8');

      return plaintext;
    } catch (error) {
      throw new Error('Decryption failed: ' + error.message);
    }
  }

  /**
   * Securely retrieves and decrypts the private key
   * @returns {string} - Decrypted private key
   */
  getPrivateKey() {
    try {
      const encryptedKey = process.env.ENCRYPTED_PRIVATE_KEY;
      if (!encryptedKey) {
        throw new Error('Private key not found in environment');
      }
      
      return this.decrypt(encryptedKey);
    } catch (error) {
      throw new Error('Failed to retrieve private key');
    }
  }

  /**
   * Encrypts a private key for storage
   * @param {string} privateKey - Private key to encrypt
   * @returns {string} - Encrypted private key for storage
   */
  encryptPrivateKey(privateKey) {
    if (!privateKey || typeof privateKey !== 'string') {
      throw new Error('Invalid private key provided');
    }
    
    // Validate private key format (basic check)
    if (!privateKey.startsWith('0x') || privateKey.length !== 66) {
      throw new Error('Invalid private key format');
    }
    
    return this.encrypt(privateKey);
  }

  /**
   * Generates a secure random string
   * @param {number} length - Length of random string
   * @returns {string} - Secure random string
   */
  generateSecureRandom(length = 32) {
    return crypto.randomBytes(length).toString('hex');
  }

  /**
   * Hashes a password securely
   * @param {string} password - Password to hash
   * @returns {Promise<string>} - Hashed password
   */
  async hashPassword(password) {
    const saltRounds = 12;
    return bcrypt.hash(password, saltRounds);
  }

  /**
   * Verifies a password against a hash
   * @param {string} password - Password to verify
   * @param {string} hash - Hash to verify against
   * @returns {Promise<boolean>} - Verification result
   */
  async verifyPassword(password, hash) {
    return bcrypt.compare(password, hash);
  }

  /**
   * Sanitizes input to prevent injection attacks
   * @param {any} input - Input to sanitize
   * @returns {any} - Sanitized input
   */
  sanitizeInput(input) {
    if (typeof input === 'string') {
      // Remove potential script tags and dangerous characters
      return input
        .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
        .replace(/[<>'"&]/g, (char) => {
          const entities = {
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#x27;',
            '&': '&amp;'
          };
          return entities[char];
        });
    }
    return input;
  }

  /**
   * Validates Ethereum address format
   * @param {string} address - Address to validate
   * @returns {boolean} - Validation result
   */
  isValidEthereumAddress(address) {
    return /^0x[a-fA-F0-9]{40}$/.test(address);
  }

  /**
   * Validates peer ID format
   * @param {string} peerId - Peer ID to validate
   * @returns {boolean} - Validation result
   */
  isValidPeerId(peerId) {
    // Basic validation for peer ID (adjust based on your specific format)
    return typeof peerId === 'string' && peerId.length > 0 && peerId.length <= 128;
  }
}

module.exports = new SecurityManager();
