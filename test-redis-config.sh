#!/bin/bash

# Test script for Redis configuration
# This script tests the Redis configuration functions from install.sh

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "🔧 Testing Redis Configuration Functions"
echo "========================================"
echo

# Test 1: Password Generation
print_status "Test 1: Testing Redis password generation..."
REDIS_PASSWORD=$(openssl rand -hex 16 | tr '[:lower:]' '[:upper:]' | head -c 24)

echo "Generated password: $REDIS_PASSWORD"
echo "Length: ${#REDIS_PASSWORD}"

if [[ "$REDIS_PASSWORD" =~ ^[A-Za-z0-9]+$ ]] && [ ${#REDIS_PASSWORD} -eq 24 ]; then
    print_success "Password generation test passed"
else
    print_error "Password generation test failed"
    exit 1
fi

echo

# Test 2: Redis Connection String
print_status "Test 2: Testing Redis connection string format..."
REDIS_URL="redis://:$REDIS_PASSWORD@localhost:6379"
echo "Connection string: $REDIS_URL"

if [[ "$REDIS_URL" =~ ^redis://:[A-Za-z0-9]+@localhost:6379$ ]]; then
    print_success "Connection string format test passed"
else
    print_error "Connection string format test failed"
    exit 1
fi

echo

# Test 3: Check if Redis is installed
print_status "Test 3: Checking Redis installation..."
if command -v redis-server &> /dev/null; then
    print_success "Redis is installed"
    redis-server --version
    
    # Test 4: Check Redis service status
    print_status "Test 4: Checking Redis service status..."
    if systemctl is-active --quiet redis-server; then
        print_success "Redis service is running"
        
        # Test 5: Test Redis connection
        print_status "Test 5: Testing Redis connection..."
        if redis-cli ping > /dev/null 2>&1; then
            print_success "Redis connection test passed (no auth)"
        else
            print_warning "Redis connection requires authentication or is not accessible"
        fi
    else
        print_warning "Redis service is not running"
    fi
else
    print_warning "Redis is not installed"
fi

echo

# Test 6: Configuration file validation
print_status "Test 6: Checking Redis configuration..."
if [ -f "/etc/redis/redis.conf" ]; then
    print_success "Redis configuration file exists"
    
    # Check for password configuration
    if grep -q "^requirepass" /etc/redis/redis.conf; then
        local existing_pass=$(grep "^requirepass" /etc/redis/redis.conf | cut -d' ' -f2)
        echo "Found existing password configuration: ${existing_pass:0:8}..."
        
        # Validate existing password format
        if [[ "$existing_pass" =~ ^[A-Za-z0-9]+$ ]]; then
            print_success "Existing password uses alphanumeric format"
        else
            print_warning "Existing password contains special characters"
        fi
    else
        print_warning "No password configuration found"
    fi
    
    # Check bind configuration
    if grep -q "^bind 127.0.0.1" /etc/redis/redis.conf; then
        print_success "Redis is configured to bind to localhost"
    else
        print_warning "Redis bind configuration not found"
    fi
    
else
    print_warning "Redis configuration file not found"
fi

echo

# Test 7: Environment file validation
print_status "Test 7: Checking environment configuration..."
if [ -f ".env" ]; then
    print_success "Environment file exists"
    
    # Check Redis password in env
    if grep -q "^REDIS_PASSWORD=" .env; then
        local env_pass=$(grep "^REDIS_PASSWORD=" .env | cut -d'=' -f2)
        echo "Found Redis password in .env: ${env_pass:0:8}..."
        
        if [[ "$env_pass" =~ ^[A-Za-z0-9]+$ ]]; then
            print_success "Environment Redis password uses alphanumeric format"
        else
            print_warning "Environment Redis password contains special characters"
        fi
    else
        print_warning "No Redis password found in .env"
    fi
    
    # Check Redis URL in env
    if grep -q "^REDIS_URL=" .env; then
        local redis_url=$(grep "^REDIS_URL=" .env | cut -d'=' -f2-)
        echo "Found Redis URL: $redis_url"
        
        if [[ "$redis_url" =~ ^redis://:[A-Za-z0-9]+@localhost:6379$ ]]; then
            print_success "Redis URL format is correct"
        else
            print_warning "Redis URL format may have issues"
        fi
    else
        print_warning "No Redis URL found in .env"
    fi
else
    print_warning "Environment file not found"
fi

echo
print_success "Redis configuration tests completed!"
echo
echo "Summary of improvements:"
echo "• Alphanumeric-only passwords (24 characters)"
echo "• No special characters that cause parsing issues"
echo "• Proper cleanup of old Redis configurations"
echo "• Validation of existing Redis installations"
echo "• Secure connection string format"
