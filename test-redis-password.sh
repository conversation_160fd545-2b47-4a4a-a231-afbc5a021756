#!/bin/bash

# Test script for Redis password generation
# This script tests the new alphanumeric-only Redis password generation

echo "🔐 Testing Redis Password Generation"
echo "===================================="
echo

# Test the password generation method used in install.sh
echo "Testing password generation method..."
REDIS_PASSWORD=$(openssl rand -hex 16 | tr '[:lower:]' '[:upper:]' | head -c 24)

echo "Generated Redis Password: $REDIS_PASSWORD"
echo "Password Length: ${#REDIS_PASSWORD}"
echo

# Validate that password contains only alphanumeric characters
if [[ "$REDIS_PASSWORD" =~ ^[A-Za-z0-9]+$ ]]; then
    echo "✅ Password contains only alphanumeric characters"
else
    echo "❌ Password contains special characters: $REDIS_PASSWORD"
    exit 1
fi

# Test multiple generations to ensure consistency
echo
echo "Testing multiple password generations..."
for i in {1..5}; do
    TEST_PASSWORD=$(openssl rand -hex 16 | tr '[:lower:]' '[:upper:]' | head -c 24)
    echo "Test $i: $TEST_PASSWORD (Length: ${#TEST_PASSWORD})"
    
    if [[ ! "$TEST_PASSWORD" =~ ^[A-Za-z0-9]+$ ]]; then
        echo "❌ Test $i failed: contains special characters"
        exit 1
    fi
done

echo
echo "✅ All password generation tests passed!"
echo

# Test Redis connection string format
echo "Testing Redis connection string format..."
REDIS_URL="redis://:$REDIS_PASSWORD@localhost:6379"
echo "Redis URL: $REDIS_URL"

# Check if URL is properly formatted (no problematic characters)
if [[ "$REDIS_URL" =~ ^redis://:[A-Za-z0-9]+@localhost:6379$ ]]; then
    echo "✅ Redis URL format is correct"
else
    echo "❌ Redis URL format is incorrect"
    exit 1
fi

echo
echo "🎉 All Redis password tests completed successfully!"
echo
echo "Key improvements:"
echo "• Passwords are exactly 24 characters long"
echo "• Only alphanumeric characters (A-Z, 0-9)"
echo "• No special characters that could cause parsing issues"
echo "• Compatible with Redis connection strings"
echo "• No escaping required in configuration files"
