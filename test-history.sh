#!/bin/bash

# Test script for join request history functionality
# This script tests the Redis-based request history storage and querying

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

HOST="localhost"
PORT="${PORT:-3000}"
BASE_URL="http://$HOST:$PORT"

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "🔍 Testing Join Request History Functionality"
echo "============================================="
echo

# Test 1: Check if server is running
print_status "Test 1: Checking if server is running..."
if curl -s "$BASE_URL/health" > /dev/null 2>&1; then
    print_success "Server is running"
else
    print_error "Server is not running or not accessible"
    echo "Please start the server with: npm start"
    exit 1
fi

echo

# Test 2: Test local-only access control
print_status "Test 2: Testing local-only access control..."

# This should work (from localhost)
response=$(curl -s -w "\nHTTP_STATUS:%{http_code}" "$BASE_URL/local/history/stats")
http_status=$(echo "$response" | grep "HTTP_STATUS:" | cut -d: -f2)

if [ "$http_status" = "200" ]; then
    print_success "Local access control working - localhost access allowed"
elif [ "$http_status" = "403" ]; then
    print_warning "Access denied - check if you're running from localhost"
else
    print_error "Unexpected response: HTTP $http_status"
fi

echo

# Test 3: Test statistics endpoint
print_status "Test 3: Testing statistics endpoint..."
stats_response=$(curl -s "$BASE_URL/local/history/stats")
if echo "$stats_response" | grep -q '"status":"ok"'; then
    print_success "Statistics endpoint working"
    echo "Response: $stats_response"
else
    print_error "Statistics endpoint failed"
    echo "Response: $stats_response"
fi

echo

# Test 4: Test recent requests endpoint
print_status "Test 4: Testing recent requests endpoint..."
recent_response=$(curl -s "$BASE_URL/local/history/recent?limit=5")
if echo "$recent_response" | grep -q '"status":"ok"'; then
    print_success "Recent requests endpoint working"
    request_count=$(echo "$recent_response" | grep -o '"count":[0-9]*' | cut -d: -f2)
    echo "Found $request_count recent requests"
else
    print_error "Recent requests endpoint failed"
    echo "Response: $recent_response"
fi

echo

# Test 5: Test query by chain
print_status "Test 5: Testing query by chain..."
chain_response=$(curl -s "$BASE_URL/local/history/chain/base?limit=5")
if echo "$chain_response" | grep -q '"status":"ok"'; then
    print_success "Chain query endpoint working"
    chain_count=$(echo "$chain_response" | grep -o '"count":[0-9]*' | cut -d: -f2)
    echo "Found $chain_count requests for 'base' chain"
else
    print_error "Chain query endpoint failed"
    echo "Response: $chain_response"
fi

echo

# Test 6: Test invalid endpoints
print_status "Test 6: Testing invalid endpoint handling..."
invalid_response=$(curl -s -w "\nHTTP_STATUS:%{http_code}" "$BASE_URL/local/history/invalid")
invalid_status=$(echo "$invalid_response" | grep "HTTP_STATUS:" | cut -d: -f2)

if [ "$invalid_status" = "404" ]; then
    print_success "Invalid endpoint properly returns 404"
else
    print_warning "Invalid endpoint returned HTTP $invalid_status (expected 404)"
fi

echo

# Test 7: Test parameter validation
print_status "Test 7: Testing parameter validation..."

# Test with missing peer ID
peer_response=$(curl -s -w "\nHTTP_STATUS:%{http_code}" "$BASE_URL/local/history/peer/")
peer_status=$(echo "$peer_response" | grep "HTTP_STATUS:" | cut -d: -f2)

if [ "$peer_status" = "404" ]; then
    print_success "Missing peer ID properly handled"
else
    print_warning "Missing peer ID returned HTTP $peer_status"
fi

echo

print_success "History functionality tests completed!"
echo
echo "📋 Available curl commands:"
echo "  curl $BASE_URL/local/history/recent?limit=10"
echo "  curl $BASE_URL/local/history/peer/PEER_ID"
echo "  curl $BASE_URL/local/history/account/0xACCOUNT_ADDRESS"
echo "  curl $BASE_URL/local/history/chain/base"
echo "  curl $BASE_URL/local/history/pool/POOL_ID"
echo "  curl $BASE_URL/local/history/stats"
echo
echo "💡 Use the query-history.sh script for easier querying:"
echo "  ./query-history.sh recent 10"
echo "  ./query-history.sh stats"
