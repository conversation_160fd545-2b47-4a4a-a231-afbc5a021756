const axios = require('axios');
const logger = require('../utils/logger');
const config = require('../config/environment');

class LibP2PService {
  constructor() {
    this.goServiceUrl = 'http://localhost:30001';
    this.pingTimeout = 15000; // 15 seconds timeout
    this.maxRetries = 3;
    this.isInitialized = false;
  }

  async initialize() {
    if (this.isInitialized) {
      return;
    }

    try {
      logger.info('Initializing LibP2P service (Go backend)...');
      
      // Test connection to Go service
      await this.healthCheck();
      this.isInitialized = true;
      
      logger.info('LibP2P service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize libp2p service:', error);
      throw new Error('LibP2P service initialization failed - Go service not available');
    }
  }

  async shutdown() {
    if (this.isInitialized) {
      this.isInitialized = false;
      logger.info('LibP2P service shutdown');
    }
  }

  async healthCheck() {
    try {
      const response = await axios.get(`${this.goServiceUrl}/health`, {
        timeout: 5000
      });
      
      logger.info('Go LibP2P service health check passed', {
        status: response.data.status,
        peerId: response.data.peerId ? response.data.peerId.substring(0, 8) + '...' : 'unknown'
      });
      
      return response.data;
    } catch (error) {
      logger.error('Go LibP2P service health check failed:', error.message);
      throw new Error('Go LibP2P service not available');
    }
  }

  async pingPeer(peerId, retryCount = 0) {
    if (!this.isInitialized) {
      await this.initialize();
    }

    try {
      // Validate peer ID format
      if (!peerId || typeof peerId !== 'string') {
        throw new Error('Invalid peer ID provided');
      }

      // Clean peer ID (remove any prefixes)
      const cleanPeerId = peerId.replace(/^\/p2p\//, '').replace(/^12D3/, '12D3');
      
      logger.info('Attempting to ping peer via Go service', {
        peerId: cleanPeerId.substring(0, 8) + '...',
        attempt: retryCount + 1
      });

      const startTime = Date.now();
      
      // Call Go service
      const response = await axios.post(`${this.goServiceUrl}/ping`, {
        peerId: cleanPeerId
      }, {
        timeout: this.pingTimeout,
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const result = response.data;
      const totalLatency = Date.now() - startTime;

      if (result.success) {
        logger.info('Ping successful via Go service', {
          peerId: cleanPeerId.substring(0, 8) + '...',
          latency: `${result.latency}ms`,
          totalLatency: `${totalLatency}ms`
        });

        return {
          success: true,
          peerId: cleanPeerId,
          latency: result.latency,
          totalLatency: totalLatency,
          timestamp: new Date().toISOString(),
          method: 'go-service'
        };
      } else {
        throw new Error(result.error || 'Ping failed');
      }

    } catch (error) {
      logger.warn('Ping attempt failed', {
        peerId: peerId ? peerId.substring(0, 8) + '...' : 'unknown',
        error: error.message,
        attempt: retryCount + 1
      });

      // Retry logic
      if (retryCount < this.maxRetries) {
        logger.info(`Retrying ping (${retryCount + 1}/${this.maxRetries})...`);
        await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1))); // Exponential backoff
        return this.pingPeer(peerId, retryCount + 1);
      }

      logger.error('Ping operation failed after all retries', {
        peerId: peerId ? peerId.substring(0, 8) + '...' : 'unknown',
        error: error.message,
        retryCount
      });

      return {
        success: false,
        peerId: peerId,
        error: error.message,
        timestamp: new Date().toISOString(),
        retryCount
      };
    }
  }

  // Alias for backward compatibility
  async validatePeerConnectivity(peerId) {
    const result = await this.pingPeer(peerId);
    return result.success;
  }

  // Get stats for compatibility
  getStats() {
    return {
      initialized: this.isInitialized,
      method: 'go-service',
      serviceUrl: this.goServiceUrl
    };
  }
}

module.exports = new LibP2PService();
