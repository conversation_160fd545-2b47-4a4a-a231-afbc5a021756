require('dotenv').config();
const Joi = require('joi');

// Environment validation schema
const envSchema = Joi.object({
  // Server Configuration
  NODE_ENV: Joi.string().valid('development', 'production', 'test').default('production'),
  PORT: Joi.number().port().default(3000),
  HOST: Joi.string().default('0.0.0.0'),

  // Security Configuration
  ENCRYPTION_KEY: Joi.string().length(64).required(),
  MASTER_PASSWORD: Joi.string().min(16).required(),
  JWT_SECRET: Joi.string().min(32).required(),
  SESSION_SECRET: Joi.string().min(32).required(),
  ENCRYPTED_PRIVATE_KEY: Joi.string().required(),

  // Rate Limiting
  RATE_LIMIT_WINDOW_MS: Joi.number().default(900000), // 15 minutes
  RATE_LIMIT_MAX_REQUESTS: Joi.number().default(100),
  SLOW_DOWN_DELAY_AFTER: Joi.number().default(50),
  SLOW_DOWN_DELAY_MS: Joi.number().default(500),

  // Blockchain Configuration
  BASE_RPC_URL: Joi.string().uri().default('https://mainnet.base.org'),
  SKALE_RPC_URL: Joi.string().uri().default('https://mainnet.skalenodes.com/v1/elated-tan-skat'),
  POOL_STORAGE_CONTRACT: Joi.string().pattern(/^0x[a-fA-F0-9]{40}$/).required(),

  // Gas Configuration
  GAS_LIMIT: Joi.number().default(500000),
  GAS_PRICE_MULTIPLIER: Joi.number().default(1.2),
  MAX_GAS_PRICE: Joi.number().default(50000000000), // 50 Gwei

  // Redis Configuration
  REDIS_URL: Joi.string().default('redis://localhost:6379'),
  REDIS_PASSWORD: Joi.string().allow(''),

  // Monitoring and Logging
  LOG_LEVEL: Joi.string().valid('error', 'warn', 'info', 'debug').default('info'),
  LOG_MAX_SIZE: Joi.string().default('20m'),
  LOG_MAX_FILES: Joi.string().default('14d'),
  ENABLE_REQUEST_LOGGING: Joi.boolean().default(true),

  // Security Headers
  CORS_ORIGIN: Joi.string().default('*'),
  TRUSTED_PROXIES: Joi.number().default(1),

  // Health Check
  HEALTH_CHECK_INTERVAL: Joi.number().default(30000)
}).unknown(true);

// Validate environment variables
const { error, value: envVars } = envSchema.validate(process.env);

if (error) {
  throw new Error(`Config validation error: ${error.message}`);
}

const config = {
  server: {
    env: envVars.NODE_ENV,
    port: envVars.PORT,
    host: envVars.HOST,
    isProduction: envVars.NODE_ENV === 'production',
    isDevelopment: envVars.NODE_ENV === 'development',
    isTest: envVars.NODE_ENV === 'test'
  },

  security: {
    encryptionKey: envVars.ENCRYPTION_KEY,
    masterPassword: envVars.MASTER_PASSWORD,
    jwtSecret: envVars.JWT_SECRET,
    sessionSecret: envVars.SESSION_SECRET,
    encryptedPrivateKey: envVars.ENCRYPTED_PRIVATE_KEY,
    corsOrigin: envVars.CORS_ORIGIN,
    trustedProxies: envVars.TRUSTED_PROXIES
  },

  rateLimit: {
    windowMs: envVars.RATE_LIMIT_WINDOW_MS,
    maxRequests: envVars.RATE_LIMIT_MAX_REQUESTS,
    slowDownDelayAfter: envVars.SLOW_DOWN_DELAY_AFTER,
    slowDownDelayMs: envVars.SLOW_DOWN_DELAY_MS
  },

  blockchain: {
    networks: {
      base: {
        name: 'base',
        rpcUrl: envVars.BASE_RPC_URL,
        chainId: 8453
      },
      skale: {
        name: 'skale',
        rpcUrl: envVars.SKALE_RPC_URL,
        chainId: 1351057110 // Skale Europa Hub chainId
      }
    },
    contracts: {
      poolStorage: envVars.POOL_STORAGE_CONTRACT
    },
    gas: {
      limit: envVars.GAS_LIMIT,
      priceMultiplier: envVars.GAS_PRICE_MULTIPLIER,
      maxPrice: envVars.MAX_GAS_PRICE
    }
  },

  redis: {
    url: envVars.REDIS_URL,
    password: envVars.REDIS_PASSWORD
  },

  logging: {
    level: envVars.LOG_LEVEL,
    maxSize: envVars.LOG_MAX_SIZE,
    maxFiles: envVars.LOG_MAX_FILES,
    enableRequestLogging: envVars.ENABLE_REQUEST_LOGGING
  },

  monitoring: {
    healthCheckInterval: envVars.HEALTH_CHECK_INTERVAL
  }
};

// Freeze configuration to prevent runtime modifications
Object.freeze(config);

module.exports = config;
