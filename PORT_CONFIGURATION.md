# 🔌 Port Configuration & Multi-Service Setup

The installation script has been updated to handle servers with existing services on port 80/443.

## 🎯 **New Port Configuration**

### **Internal Application Port**
- **Pool Server**: Runs on port `3001` (internal)
- **Binding**: `127.0.0.1:3001` (localhost only)
- **Access**: Only accessible through nginx reverse proxy

### **External Ports (Nginx)**
- **HTTP**: Port `80` (shared with other services)
- **HTTPS**: Port `443` (shared with other services)
- **SSL Validation**: Uses webroot method (no port conflicts)

## 🔄 **Architecture Overview**

```
Internet → Nginx (Port 80/443) → Pool Server (Port 3001)
                ↓
         Other Services (existing)
```

### **Request Flow**
1. **Client** → `https://pools.fx.land/join`
2. **Nginx** → Receives request on port 443
3. **Proxy** → Forwards to `127.0.0.1:3001/join`
4. **Pool Server** → Processes request internally
5. **Response** → Returns through nginx to client

## 🛡️ **Security Benefits**

### **Network Isolation**
- **Internal Only**: Pool server not directly accessible from internet
- **Nginx Protection**: All requests filtered through nginx security
- **Rate Limiting**: Applied at nginx level before reaching application
- **SSL Termination**: Handled by nginx, not application

### **Service Isolation**
- **Port Separation**: No conflicts with existing services
- **Process Isolation**: Runs under dedicated `mainnet` user
- **Resource Limits**: Systemd controls applied
- **Independent Restart**: Can restart without affecting other services

## 🔧 **SSL Certificate Management**

### **Webroot Method (No Port Conflicts)**
```bash
# Certificate validation path
/.well-known/acme-challenge/ → /var/www/certbot/

# Nginx serves validation files
location /.well-known/acme-challenge/ {
    root /var/www/certbot;
    try_files $uri =404;
}
```

### **Automatic Renewal**
- **Method**: Webroot (no service interruption)
- **Schedule**: Automatic via cron
- **Validation**: Through existing nginx on port 80
- **Reload**: Nginx reloads certificates automatically

## 📋 **Configuration Details**

### **Environment Variables**
```bash
# Pool server configuration
NODE_ENV=production
PORT=3001                    # Internal port
HOST=127.0.0.1              # Localhost only
```

### **Nginx Upstream**
```nginx
upstream pool_server {
    server 127.0.0.1:3001;  # Internal port
    keepalive 32;
}
```

### **SSL Configuration**
```nginx
# HTTP (port 80) - shared with other services
server {
    listen 80;
    server_name pools.fx.land;
    
    # SSL validation (shared)
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }
    
    # Pool server health check
    location /health {
        proxy_pass http://pool_server;
    }
    
    # Redirect to HTTPS
    location / {
        return 301 https://$server_name$request_uri;
    }
}

# HTTPS (port 443) - shared with other services
server {
    listen 443 ssl http2;
    server_name pools.fx.land;
    
    # Pool server endpoints
    location / {
        proxy_pass http://pool_server;
        # Security headers and rate limiting
    }
}
```

## 🔍 **Health Checks & Monitoring**

### **Internal Health Check**
```bash
# Direct application check
curl http://localhost:3001/health

# Through nginx proxy
curl https://pools.fx.land/health
```

### **Service Status**
```bash
# Pool server service
systemctl status mainnet-pool-server

# Nginx status
systemctl status nginx

# Port usage
netstat -tlnp | grep :3001
netstat -tlnp | grep :80
netstat -tlnp | grep :443
```

## 🚨 **Troubleshooting**

### **Port Conflicts**
If you see port conflicts:
```bash
# Check what's using port 80
sudo lsof -i :80

# Check what's using port 443
sudo lsof -i :443

# Check internal port
sudo lsof -i :3001
```

### **SSL Issues**
If SSL certificate fails:
```bash
# Check nginx configuration
sudo nginx -t

# Check webroot directory
ls -la /var/www/certbot/

# Manual certificate test
sudo certbot certonly --webroot \
  --webroot-path=/var/www/certbot \
  --email <EMAIL> \
  -d pools.fx.land --dry-run
```

### **Service Communication**
If nginx can't reach pool server:
```bash
# Test internal connection
curl http://127.0.0.1:3001/health

# Check pool server logs
sudo -u mainnet pm2 logs

# Check nginx error logs
sudo tail -f /var/log/nginx/error.log
```

## 🔧 **Manual Configuration**

### **Change Internal Port**
To use a different internal port:

1. **Update environment**:
   ```bash
   sudo nano /opt/mainnet/.env
   # Change PORT=3001 to PORT=3002
   ```

2. **Update nginx**:
   ```bash
   sudo nano /etc/nginx/sites-available/mainnet-pool-server
   # Change server 127.0.0.1:3001 to server 127.0.0.1:3002
   ```

3. **Restart services**:
   ```bash
   sudo systemctl restart mainnet-pool-server
   sudo systemctl reload nginx
   ```

### **Add Additional Domains**
To serve multiple domains:

1. **Get additional certificates**:
   ```bash
   sudo certbot certonly --webroot \
     --webroot-path=/var/www/certbot \
     -d api.pools.fx.land
   ```

2. **Update nginx configuration** to include new domains

3. **Test and reload**:
   ```bash
   sudo nginx -t
   sudo systemctl reload nginx
   ```

## ✅ **Verification Checklist**

After installation:
- [ ] Pool server running on internal port 3001
- [ ] Nginx serving on ports 80/443
- [ ] SSL certificate valid and auto-renewing
- [ ] Health check accessible via HTTPS
- [ ] No port conflicts with existing services
- [ ] All services start automatically on boot

This configuration allows the pool server to coexist with other services while maintaining security and proper SSL certificate management.
