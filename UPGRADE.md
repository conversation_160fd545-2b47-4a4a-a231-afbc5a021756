# Application Upgrade Guide

This guide explains how to safely upgrade the pool server application using the automated upgrade scripts.

## Overview

The upgrade system provides two scripts for different environments:

- **`upgrade.sh`** - Production upgrade script (requires root access)
- **`upgrade-dev.sh`** - Development upgrade script (can run as regular user)

Both scripts include automatic backup, rollback capabilities, and verification steps.

## Features

### ✅ **Safety Features**
- Automatic backup creation before upgrade
- Rollback capability if upgrade fails
- Service verification after upgrade
- Minimal downtime approach

### 🔧 **What Gets Updated**
- Application code (via git pull)
- Node.js dependencies
- LibP2P service binary
- Redis cache clearing
- Database migrations (if any)

### 📊 **New Features Added**
- Redis-based request history storage
- Local-only history query API endpoints
- Full peerId, account, and poolId storage (no masking)
- 30-day automatic data retention

## Quick Start

### For Production Servers (with systemd services)

```bash
# Test the upgrade system first
sudo ./test-upgrade.sh

# Run the production upgrade
sudo ./upgrade.sh
```

### For Development Environments

```bash
# Test the upgrade system first
./test-upgrade.sh

# Run the development upgrade
./upgrade-dev.sh
```

## Detailed Usage

### 1. Pre-Upgrade Preparation

```bash
# Make scripts executable
chmod +x upgrade.sh upgrade-dev.sh test-upgrade.sh query-history.sh

# Test the upgrade system
./test-upgrade.sh

# Check current application status
curl http://localhost:3000/health
```

### 2. Production Upgrade Process

The production script (`upgrade.sh`) performs these steps:

1. **Verification** - Checks root access and existing services
2. **Backup** - Creates complete backup of application and configuration
3. **Service Stop** - Gracefully stops pool-server and libp2p-service
4. **Code Update** - Pulls latest code from git repository
5. **Dependencies** - Updates Node.js packages
6. **Build** - Rebuilds LibP2P service if needed
7. **Migrations** - Clears Redis cache and runs any migrations
8. **Service Start** - Restarts all services
9. **Verification** - Tests health endpoints and functionality
10. **Cleanup** - Removes old backups

```bash
sudo ./upgrade.sh
```

### 3. Development Upgrade Process

The development script (`upgrade-dev.sh`) is simpler and doesn't require root:

1. **Directory Check** - Verifies we're in the right location
2. **Backup** - Creates backup of current code
3. **Stop** - Stops PM2 processes or running applications
4. **Update** - Pulls latest code and updates dependencies
5. **Build** - Rebuilds LibP2P service
6. **Cache Clear** - Clears Redis cache
7. **Verification** - Checks new files and dependencies

```bash
./upgrade-dev.sh
```

## Backup and Rollback

### Automatic Backups

Backups are created automatically in `/tmp/` with timestamps:
- `/tmp/pool-server-backup-YYYYMMDD_HHMMSS/` (production)
- `/tmp/pool-server-dev-backup-YYYYMMDD_HHMMSS/` (development)

### Manual Rollback

If you need to rollback manually:

```bash
# Stop services
sudo systemctl stop pool-server libp2p-service

# Restore from backup
sudo rm -rf /opt/mainnet
sudo cp -r /tmp/pool-server-backup-YYYYMMDD_HHMMSS/app /opt/mainnet

# Restart services
sudo systemctl start pool-server libp2p-service
```

## Verification

After upgrade, verify the system is working:

```bash
# Check service status
sudo systemctl status pool-server
sudo systemctl status libp2p-service

# Test health endpoint
curl http://localhost:3000/health

# Test new history functionality
curl http://localhost:3000/local/history/stats
./query-history.sh stats
```

## New History Features

The upgrade adds comprehensive request history tracking:

### Storage
- All join requests stored in Redis with 30-day TTL
- Full peerId, account, and poolId data (no masking)
- Request timestamps, IP addresses, and results
- Multiple indexes for efficient querying

### Query API (localhost-only)
```bash
# Recent requests
curl "http://localhost:3000/local/history/recent?limit=10"

# By peer ID
curl "http://localhost:3000/local/history/peer/12D3KooWABC123..."

# By account
curl "http://localhost:3000/local/history/account/0x1234567890abcdef..."

# By chain
curl "http://localhost:3000/local/history/chain/base"

# Statistics
curl "http://localhost:3000/local/history/stats"
```

### Helper Script
```bash
# Use the query helper script
./query-history.sh recent 10
./query-history.sh peer 12D3KooWABC123...
./query-history.sh account 0x1234567890abcdef...
./query-history.sh stats
```

## Troubleshooting

### Common Issues

1. **Permission Denied**
   ```bash
   chmod +x upgrade.sh upgrade-dev.sh
   ```

2. **Git Repository Issues**
   ```bash
   git stash
   git fetch origin
   git reset --hard origin/main
   ```

3. **Service Start Failures**
   ```bash
   # Check logs
   sudo journalctl -u pool-server -f
   sudo journalctl -u libp2p-service -f
   ```

4. **Redis Connection Issues**
   ```bash
   # Test Redis
   redis-cli ping
   redis-cli -a YOUR_PASSWORD ping
   ```

### Log Files

Upgrade logs are saved to `/tmp/upgrade-YYYYMMDD_HHMMSS.log`

```bash
# View upgrade log
tail -f /tmp/upgrade-*.log

# View application logs
tail -f /opt/mainnet/logs/combined-$(date +%Y-%m-%d).log
```

### Recovery Steps

If upgrade fails and automatic rollback doesn't work:

1. **Stop all services**
   ```bash
   sudo systemctl stop pool-server libp2p-service
   pkill -f "node.*server.js"
   ```

2. **Restore from backup**
   ```bash
   sudo rm -rf /opt/mainnet
   sudo cp -r /tmp/pool-server-backup-*/app /opt/mainnet
   ```

3. **Restart services**
   ```bash
   sudo systemctl daemon-reload
   sudo systemctl start pool-server libp2p-service
   ```

## Best Practices

### Before Upgrading
- ✅ Test in development environment first
- ✅ Ensure stable internet connection
- ✅ Notify users of maintenance window
- ✅ Check disk space for backups
- ✅ Verify Redis is running and accessible

### During Upgrade
- ✅ Monitor the upgrade process
- ✅ Don't interrupt the script
- ✅ Check logs if issues occur

### After Upgrade
- ✅ Verify all services are running
- ✅ Test critical functionality
- ✅ Monitor logs for errors
- ✅ Test new history features
- ✅ Keep backup until confirmed stable

## Support

If you encounter issues:

1. Check the upgrade log file
2. Verify system requirements
3. Test individual components
4. Use the rollback procedure if needed
5. Check application logs for detailed error information

The upgrade scripts are designed to be safe and recoverable, but always ensure you have proper backups before proceeding.
