const { ethers } = require('ethers');
const config = require('../config/environment');
const security = require('../config/security');
const logger = require('../utils/logger');
const PoolStorageABI = require('../contracts/PoolStorage.json');
const libp2pService = require('./libp2p');
const { peerIdToBytes32 } = require('../utils/peerIdConverter');

class BlockchainService {
  constructor() {
    this.providers = {};
    this.contracts = {};
    this.wallet = null;
    this.gasEstimationCache = new Map();
    this.initializeProviders();
    this.initializeWallet();
    this.initializeContracts();
  }

  initializeProviders() {
    try {
      // Initialize Base provider
      this.providers.base = new ethers.JsonRpcProvider(
        config.blockchain.networks.base.rpcUrl,
        {
          chainId: config.blockchain.networks.base.chainId,
          name: config.blockchain.networks.base.name
        }
      );

      // Initialize Skale provider
      this.providers.skale = new ethers.JsonRpcProvider(
        config.blockchain.networks.skale.rpcUrl,
        {
          chainId: config.blockchain.networks.skale.chainId,
          name: config.blockchain.networks.skale.name
        }
      );

      logger.info('Blockchain providers initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize blockchain providers:', error);
      throw new Error('Blockchain provider initialization failed');
    }
  }

  initializeWallet() {
    try {
      const privateKey = security.getPrivateKey();

      if (!privateKey || !privateKey.startsWith('0x') || privateKey.length !== 66) {
        throw new Error('Invalid private key format');
      }

      // Create wallet instances for each network
      this.wallet = {
        base: new ethers.Wallet(privateKey, this.providers.base),
        skale: new ethers.Wallet(privateKey, this.providers.skale)
      };

      logger.info('Wallet initialized successfully', {
        address: this.wallet.base.address
      });
    } catch (error) {
      logger.error('Failed to initialize wallet:', error);
      throw new Error('Wallet initialization failed');
    }
  }

  /**
   * Reset and reinitialize wallet (useful for fixing encryption issues)
   * @returns {Object} - Wallet reset result with address
   */
  async resetWallet() {
    try {
      logger.info('Resetting wallet...');

      // Clear existing wallet data
      this.wallet = null;
      this.gasEstimationCache.clear();

      // Reinitialize wallet
      this.initializeWallet();

      // Reinitialize contracts with new wallet
      this.initializeContracts();

      logger.info('Wallet reset completed successfully', {
        address: this.wallet.base.address
      });

      return {
        address: this.wallet.base.address,
        networks: ['base', 'skale'],
        resetTimestamp: new Date().toISOString()
      };
    } catch (error) {
      logger.error('Failed to reset wallet:', error);
      throw new Error('Wallet reset failed: ' + error.message);
    }
  }

  initializeContracts() {
    try {
      const contractAddress = config.blockchain.contracts.poolStorage;
      
      // Initialize contracts for each network
      this.contracts.base = new ethers.Contract(
        contractAddress,
        PoolStorageABI.abi,
        this.wallet.base
      );

      this.contracts.skale = new ethers.Contract(
        contractAddress,
        PoolStorageABI.abi,
        this.wallet.skale
      );

      logger.info('Smart contracts initialized successfully', {
        contractAddress
      });
    } catch (error) {
      logger.error('Failed to initialize contracts:', error);
      throw new Error('Contract initialization failed');
    }
  }

  async validateChain(chain) {
    const supportedChains = ['base', 'skale'];
    if (!supportedChains.includes(chain)) {
      throw new Error(`Chain "${chain}" is not supported. Supported chains: ${supportedChains.join(', ')}`);
    }
    return true;
  }

  async validatePoolExists(poolId, chain) {
    try {
      const contract = this.contracts[chain];

      logger.info('Validating pool existence using pools storage', {
        poolId,
        chain,
        contractAddress: contract.target || contract.address,
        walletAddress: this.wallet[chain].address
      });

      // Use the pools storage variable instead of non-existent getPool method
      const poolData = await contract.pools(poolId);

      logger.info('Pool storage query result', {
        poolId,
        chain,
        poolData: {
          creator: poolData[0],
          id: poolData[1],
          maxChallengeResponsePeriod: poolData[2],
          memberCount: poolData[3],
          maxMembers: poolData[4],
          requiredTokens: poolData[5].toString(),
          minPingTime: poolData[6].toString(),
          name: poolData[7],
          region: poolData[8]
        },
        poolExists: poolData[1] !== 0
      });

      if (poolData[1] === 0) {
        throw new Error(`Pool ${poolId} does not exist`);
      }

      return {
        creator: poolData[0],
        id: poolData[1],
        maxChallengeResponsePeriod: poolData[2],
        memberCount: poolData[3],
        maxMembers: poolData[4],
        requiredTokens: poolData[5],
        minPingTime: poolData[6],
        name: poolData[7],
        region: poolData[8]
      };
    } catch (error) {
      if (error.message.includes('does not exist')) {
        throw error;
      }
      logger.error('Pool validation network error', {
        chain,
        poolId,
        contractAddress: contract.target || contract.address,
        error: error.message,
        errorCode: error.code,
        errorData: error.data
      });
      logger.blockchain.networkError({
        chain,
        poolId,
        error: error.message
      });
      throw new Error('Failed to validate pool existence');
    }
  }

  async checkExistingMembership(poolId, account, peerId, chain) {
    try {
      const contract = this.contracts[chain];
      const peerIdBytes32 = await peerIdToBytes32(peerId);

      logger.info('Checking existing membership', {
        poolId,
        account,
        peerId,
        peerIdBytes32,
        chain,
        contractAddress: contract.target || contract.address
      });

      // Use the correct isPeerIdMemberOfPool method
      const membershipResult = await contract.isPeerIdMemberOfPool(poolId, peerIdBytes32);

      logger.info('Membership check result', {
        poolId,
        peerId,
        isMember: membershipResult[0],
        memberAddress: membershipResult[1],
        chain
      });

      if (membershipResult[0]) {
        throw new Error(`Peer is already a member of pool ${poolId} with address ${membershipResult[1]}`);
      }

      return true;
    } catch (error) {
      if (error.message.includes('already a member')) {
        throw error;
      }
      logger.error('Membership check failed', {
        poolId,
        account,
        peerId: peerId.substring(0, 8) + '...',
        chain,
        error: error.message,
        errorCode: error.code
      });
      throw new Error('Failed to check existing membership');
    }
  }

  async estimateGas(contract, method, params, chain) {
    try {
      const cacheKey = `${chain}-${method}-${JSON.stringify(params)}`;
      
      // Check cache first (valid for 5 minutes)
      if (this.gasEstimationCache.has(cacheKey)) {
        const cached = this.gasEstimationCache.get(cacheKey);
        if (Date.now() - cached.timestamp < 300000) { // 5 minutes
          return cached.gasLimit;
        }
      }

      const gasEstimate = await contract[method].estimateGas(...params);
      const gasLimit = Math.floor(Number(gasEstimate) * config.blockchain.gas.priceMultiplier);
      
      // Cache the result
      this.gasEstimationCache.set(cacheKey, {
        gasLimit,
        timestamp: Date.now()
      });
      
      // Clean cache if it gets too large
      if (this.gasEstimationCache.size > 100) {
        const oldestKey = this.gasEstimationCache.keys().next().value;
        this.gasEstimationCache.delete(oldestKey);
      }
      
      return gasLimit;
    } catch (error) {
      logger.blockchain.gasEstimationError({
        chain,
        method,
        error: error.message
      });
      
      // Return default gas limit if estimation fails
      return config.blockchain.gas.limit;
    }
  }

  async getCurrentGasPrice(chain) {
    try {
      const provider = this.providers[chain];
      const feeData = await provider.getFeeData();

      let gasPrice = feeData.gasPrice;

      // For EIP-1559 networks, use maxFeePerGas
      if (feeData.maxFeePerGas) {
        gasPrice = feeData.maxFeePerGas;
      }

      // Apply multiplier and cap
      const adjustedGasPrice = Math.floor(Number(gasPrice) * config.blockchain.gas.priceMultiplier);
      const cappedGasPrice = Math.min(adjustedGasPrice, config.blockchain.gas.maxPrice);

      return cappedGasPrice;
    } catch (error) {
      logger.blockchain.gasEstimationError({
        chain,
        error: error.message
      });

      // Return a reasonable default
      return ***********; // 20 Gwei
    }
  }

  async addMemberToPool({ peerId, account, chain, poolId }) {
    const startTime = Date.now();
    let transactionHash = null;

    try {
      // Validate inputs
      await this.validateChain(chain);

      if (!security.isValidEthereumAddress(account)) {
        throw new Error('Invalid Ethereum address format');
      }

      if (!security.isValidPeerId(peerId)) {
        throw new Error('Invalid peer ID format');
      }

      if (!Number.isInteger(poolId) || poolId <= 0) {
        throw new Error('Invalid pool ID');
      }

      // Validate pool exists
      const pool = await this.validatePoolExists(poolId, chain);

      // Check for existing membership/requests
      await this.checkExistingMembership(poolId, account, peerId, chain);

      // Check pool capacity
      if (pool.maxMembers > 0 && pool.memberCount >= pool.maxMembers) {
        throw new Error('Pool has reached maximum capacity');
      }

      // Validate peer connectivity through libp2p relay
      logger.info('Validating peer connectivity', {
        peerId: peerId.substring(0, 8) + '...',
        poolId,
        chain
      });

      const isPeerReachable = await libp2pService.validatePeerConnectivity(peerId);

      if (!isPeerReachable) {
        throw new Error('Peer is not reachable through the relay network. Please ensure your node is online and connected to the relay.');
      }

      logger.info('Peer connectivity validated successfully', {
        peerId: peerId.substring(0, 8) + '...',
        poolId,
        chain
      });

      const contract = this.contracts[chain];
      const peerIdBytes32 = await peerIdToBytes32(peerId);

      // Log detailed transaction parameters
      logger.info('Preparing contract transaction', {
        chain,
        poolId,
        account,
        peerId,
        peerIdBytes32,
        walletAddress: this.wallet[chain].address,
        contractAddress: contract.target || contract.address,
        method: 'addMember'
      });

      // Estimate gas
      const gasLimit = await this.estimateGas(
        contract,
        'addMember',
        [poolId, account, peerIdBytes32],
        chain
      );

      // Get current gas price
      const gasPrice = await this.getCurrentGasPrice(chain);

      const nonce = await this.providers[chain].getTransactionCount(this.wallet[chain].address, 'pending');

      logger.info('Sending transaction with full details', {
        chain,
        poolId,
        account,
        peerId,
        peerIdBytes32,
        walletAddress: this.wallet[chain].address,
        contractAddress: contract.target || contract.address,
        gasLimit,
        gasPrice: gasPrice.toString(),
        nonce,
        method: 'addMember',
        parameters: [poolId, account, peerIdBytes32]
      });

      // Send transaction
      const transaction = await contract.addMember(poolId, account, peerIdBytes32, {
        gasLimit,
        gasPrice,
        nonce
      });

      transactionHash = transaction.hash;

      logger.info('Transaction submitted', {
        chain,
        poolId,
        transactionHash,
        gasLimit,
        gasPrice: gasPrice.toString()
      });

      // Wait for confirmation with timeout
      const receipt = await Promise.race([
        transaction.wait(1), // Wait for 1 confirmation
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Transaction timeout')), 60000) // 60 second timeout
        )
      ]);

      if (receipt.status === 1) {
        const duration = Date.now() - startTime;

        logger.blockchain.transactionConfirmed({
          chain,
          poolId,
          transactionHash,
          blockNumber: receipt.blockNumber,
          gasUsed: receipt.gasUsed.toString(),
          duration: `${duration}ms`
        });

        return {
          success: true,
          transactionHash,
          blockNumber: receipt.blockNumber,
          gasUsed: receipt.gasUsed.toString()
        };
      } else {
        throw new Error('Transaction failed');
      }

    } catch (error) {
      const duration = Date.now() - startTime;

      logger.blockchain.transactionFailed({
        chain,
        poolId,
        account: account ? account.substring(0, 6) + '...' + account.substring(38) : 'unknown',
        transactionHash,
        error: error.message,
        duration: `${duration}ms`
      });

      // Return structured error response
      return {
        success: false,
        error: this.formatErrorMessage(error.message),
        transactionHash
      };
    }
  }

  formatErrorMessage(errorMessage) {
    // Map common blockchain errors to user-friendly messages
    const errorMappings = {
      'insufficient funds': 'Insufficient funds for transaction',
      'gas required exceeds allowance': 'Transaction requires too much gas',
      'nonce too low': 'Transaction nonce error, please try again',
      'replacement transaction underpriced': 'Transaction fee too low',
      'already known': 'Transaction already submitted',
      'Pool has reached maximum capacity': 'Pool has reached maximum capacity',
      'Active join request already exists': 'You already have a pending join request for this pool',
      'does not exist': 'Pool does not exist',
      'Chain "': 'Unsupported blockchain network',
      'Invalid': 'Invalid request parameters',
      'Peer is not reachable': 'Your node is not reachable through the relay network. Please ensure your node is online and properly connected.',
      'libp2p': 'Network connectivity error. Please check your node connection.',
      'relay network': 'Unable to connect through relay network. Please try again later.'
    };

    for (const [key, message] of Object.entries(errorMappings)) {
      if (errorMessage.toLowerCase().includes(key.toLowerCase())) {
        return message;
      }
    }

    // Return generic error for unknown errors in production
    return config.server.isProduction
      ? 'Transaction failed. Please try again later.'
      : errorMessage;
  }

  // Health check method
  async healthCheck() {
    const results = {};

    // Check blockchain providers
    for (const [chainName, provider] of Object.entries(this.providers)) {
      try {
        const blockNumber = await provider.getBlockNumber();
        const network = await provider.getNetwork();

        results[chainName] = {
          status: 'healthy',
          blockNumber,
          chainId: Number(network.chainId),
          latency: Date.now() // This would need proper latency measurement
        };
      } catch (error) {
        results[chainName] = {
          status: 'unhealthy',
          error: error.message
        };
      }
    }

    // Check libp2p service
    try {
      const libp2pHealth = await libp2pService.healthCheck();
      results.libp2p = libp2pHealth;
    } catch (error) {
      results.libp2p = {
        status: 'unhealthy',
        error: error.message
      };
    }

    return results;
  }
}

module.exports = new BlockchainService();
