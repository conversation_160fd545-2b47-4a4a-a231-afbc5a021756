#!/bin/bash

# Secure Pool Server Installation Script
# This script installs and configures the secure pool server with SSL support

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
INSTALL_DIR="/opt/mainnet"
SERVICE_NAME="mainnet-pool-server"
APP_USER="root"
NGINX_SITES_DIR="/etc/nginx/sites-available"
NGINX_ENABLED_DIR="/etc/nginx/sites-enabled"
SSL_DIR="/etc/ssl/mainnet"
INTERNAL_PORT="3001"  # Internal port for the pool server (avoiding port 80/443 conflicts)

# Logging
LOG_FILE="/var/log/secure-pool-server-install.log"

# Create the log file if it doesn't exist
if [ ! -f "$LOG_FILE" ]; then
  sudo touch "$LOG_FILE"
  sudo chmod 644 "$LOG_FILE"
  sudo chown "$USER:$USER" "$LOG_FILE"  # or use 'root:root' if running as root
fi

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1" | tee -a "$LOG_FILE"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

# Function to check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        print_error "This script must be run as root (use sudo)"
        exit 1
    fi
}

# Function to clean up previous installation
cleanup_previous_installation() {
    print_status "Checking for previous installation..."

    local cleanup_needed=false

    # Check if service exists
    if systemctl list-unit-files | grep -q "$SERVICE_NAME.service"; then
        print_warning "Found existing service: $SERVICE_NAME"
        cleanup_needed=true
    fi

    # Check if user exists
    if id "$APP_USER" &>/dev/null; then
        print_warning "Found existing user: $APP_USER"
        cleanup_needed=true
    fi

    # Check if installation directory exists
    if [[ -d "$INSTALL_DIR" ]]; then
        print_warning "Found existing installation directory: $INSTALL_DIR"
        cleanup_needed=true
    fi

    # Check if nginx config exists
    if [[ -f "$NGINX_SITES_DIR/$SERVICE_NAME" ]]; then
        print_warning "Found existing nginx configuration"
        cleanup_needed=true
    fi

    if [[ "$cleanup_needed" == "true" ]]; then
        echo
        print_warning "Previous installation detected. This will:"
        echo "   • Stop and remove existing services"
        echo "   • Clean up PM2 processes"
        echo "   • Remove existing installation directory"
        echo "   • Update nginx configuration"
        echo "   • Preserve SSL certificates"
        echo
        read -p "Do you want to continue with cleanup? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            echo "Installation cancelled."
            exit 0
        fi

        perform_cleanup
    else
        print_success "No previous installation found"
    fi
}

# Function to perform the actual cleanup
perform_cleanup() {
    print_status "Performing cleanup of previous installation..."

    # Stop and disable systemd service
    if systemctl list-unit-files | grep -q "$SERVICE_NAME.service"; then
        print_status "Stopping and disabling service: $SERVICE_NAME"
        systemctl stop "$SERVICE_NAME" 2>/dev/null || true
        systemctl disable "$SERVICE_NAME" 2>/dev/null || true
        rm -f "/etc/systemd/system/$SERVICE_NAME.service"
        systemctl daemon-reload
    fi

    # Clean up PM2 processes for the user
    if id "$APP_USER" &>/dev/null; then
        print_status "Cleaning up PM2 processes for user: $APP_USER"

        # Kill all PM2 processes system-wide
        pkill -f pm2 2>/dev/null || true

        # Clean up PM2 processes
        export PM2_HOME=$INSTALL_DIR/.pm2 && pm2 kill 2>/dev/null || true
        export PM2_HOME=$INSTALL_DIR/.pm2 && pm2 delete all 2>/dev/null || true

        # Remove PM2 directories
        rm -rf "/home/<USER>/.pm2" 2>/dev/null || true
        if [[ -d "$INSTALL_DIR/.pm2" ]]; then
            rm -rf "$INSTALL_DIR/.pm2"
        fi

        # Kill any remaining node processes for this user
        pkill -u "$APP_USER" -f "node.*server.js" 2>/dev/null || true
    fi

    # Stop nginx temporarily (will be restarted later)
    print_status "Stopping nginx temporarily"
    systemctl stop nginx 2>/dev/null || true

    # Remove nginx configuration
    if [[ -f "$NGINX_SITES_DIR/$SERVICE_NAME" ]]; then
        print_status "Removing nginx configuration"
        rm -f "$NGINX_SITES_DIR/$SERVICE_NAME"
        rm -f "$NGINX_ENABLED_DIR/$SERVICE_NAME"
    fi

    # Backup and remove installation directory
    if [[ -d "$INSTALL_DIR" ]]; then
        print_status "Backing up and removing installation directory"

        # Create backup of .env file if it exists
        if [[ -f "$INSTALL_DIR/.env" ]]; then
            cp "$INSTALL_DIR/.env" "/tmp/.env.backup.$(date +%Y%m%d_%H%M%S)" 2>/dev/null || true
            print_status "Environment file backed up to /tmp/"
        fi

        # Create backup of logs if they exist
        if [[ -d "$INSTALL_DIR/logs" ]]; then
            mkdir -p "/tmp/mainnet_logs_backup_$(date +%Y%m%d_%H%M%S)"
            cp -r "$INSTALL_DIR/logs/"* "/tmp/mainnet_logs_backup_$(date +%Y%m%d_%H%M%S)/" 2>/dev/null || true
            print_status "Logs backed up to /tmp/"
        fi

        # Remove installation directory
        rm -rf "$INSTALL_DIR"
    fi

    # Clean up user resource limits
    if [[ -f "/etc/security/limits.conf" ]]; then
        sed -i "/^$APP_USER /d" /etc/security/limits.conf 2>/dev/null || true
    fi

    # Remove fail2ban configuration
    if [[ -f "/etc/fail2ban/jail.d/$SERVICE_NAME.conf" ]]; then
        print_status "Removing fail2ban configuration"
        rm -f "/etc/fail2ban/jail.d/$SERVICE_NAME.conf"
        systemctl restart fail2ban 2>/dev/null || true
    fi

    # Note: We don't remove the user account to preserve UID/GID consistency
    # and avoid potential permission issues with recreated accounts

    print_success "Cleanup completed successfully"
    echo
}

# Function to check system requirements
check_requirements() {
    print_status "Checking system requirements..."
    
    # Check OS
    if [[ ! -f /etc/os-release ]]; then
        print_error "Cannot determine OS version"
        exit 1
    fi
    
    . /etc/os-release
    if [[ "$ID" != "ubuntu" && "$ID" != "debian" ]]; then
        print_warning "This script is designed for Ubuntu/Debian. Proceeding anyway..."
    fi
    
    # Check architecture
    ARCH=$(uname -m)
    if [[ "$ARCH" != "x86_64" ]]; then
        print_warning "This script is optimized for x86_64 architecture"
    fi
    
    print_success "System requirements check completed"
}

# Function to install system dependencies
install_dependencies() {
    print_status "Installing system dependencies..."
    
    # Update package list
    apt-get update -y >> "$LOG_FILE" 2>&1
    
    # Install required packages
    apt-get install -y \
        curl \
        wget \
        git \
        nginx \
        certbot \
        python3-certbot-nginx \
        ufw \
        fail2ban \
        redis-server \
        build-essential \
        software-properties-common \
        apt-transport-https \
        ca-certificates \
        gnupg \
        lsb-release \
        golang-go >> "$LOG_FILE" 2>&1
    
    print_success "System dependencies installed"
}

# Function to build Go libp2p service
build_libp2p_service() {
    print_status "Building Go LibP2P service..."

    # Check if Go is installed
    if ! command -v go &> /dev/null; then
        print_error "Go is not installed"
        exit 1
    fi

    # Navigate to libp2p service directory
    cd "$INSTALL_DIR/libp2p-service"

    # Initialize Go module and download dependencies
    print_status "Downloading Go dependencies..."
    go mod tidy >> "$LOG_FILE" 2>&1

    # Build the service
    print_status "Compiling Go LibP2P service..."
    go build -o libp2p-service main.go >> "$LOG_FILE" 2>&1

    if [[ $? -eq 0 ]]; then
        print_success "Go LibP2P service built successfully"
        chmod +x libp2p-service
    else
        print_error "Failed to build Go LibP2P service"
        exit 1
    fi

    cd - > /dev/null
}

# Function to install Node.js
install_nodejs() {
    print_status "Installing Node.js 18..."
    
    # Add NodeSource repository
    curl -fsSL https://deb.nodesource.com/setup_18.x | bash - >> "$LOG_FILE" 2>&1
    
    # Install Node.js
    apt-get install -y nodejs >> "$LOG_FILE" 2>&1
    
    # Install PM2 globally
    npm install -g pm2
    chown -R "$APP_USER:$APP_USER" "$(npm root -g)" >> "$LOG_FILE" 2>&1
    
    # Verify installation
    NODE_VERSION=$(node --version)
    NPM_VERSION=$(npm --version)
    PM2_VERSION=$(pm2 --version)
    
    print_success "Node.js $NODE_VERSION, npm $NPM_VERSION, PM2 $PM2_VERSION installed"
}

# Function to create application user
create_app_user() {
    print_status "Creating dedicated application user '$APP_USER'..."

    # Create user if it doesn't exist
    if ! id "$APP_USER" &>/dev/null; then
        # Create system user with bash shell (needed for PM2)
        useradd -r \
            -s /bin/bash \
            -d "$INSTALL_DIR" \
            -c "Mainnet Pool Server" \
            -U \
            "$APP_USER"

        # Lock the account to prevent login but allow sudo execution
        passwd -l "$APP_USER" >/dev/null 2>&1

        # Create additional security groups
        groupadd -f mainnet-logs
        usermod -a -G mainnet-logs "$APP_USER"

        # Add user to necessary groups for node execution
        usermod -a -G users "$APP_USER"
        usermod -a -G staff "$APP_USER" 2>/dev/null || true

        print_success "User '$APP_USER' created with restricted permissions"
    else
        print_warning "User '$APP_USER' already exists"
        # Update shell if it's /bin/false
        if [[ "$(getent passwd "$APP_USER" | cut -d: -f7)" == "/bin/false" ]]; then
            usermod -s /bin/bash "$APP_USER"
            print_status "Updated shell for existing user"
        fi

        # Ensure user is in necessary groups
        usermod -a -G users "$APP_USER" 2>/dev/null || true
        usermod -a -G staff "$APP_USER" 2>/dev/null || true
        usermod -a -G mainnet-logs "$APP_USER" 2>/dev/null || true
    fi

    # Fix Node.js permissions (critical for PM2)
    print_status "Fixing Node.js permissions for $APP_USER..."

    # Ensure node binary is executable
    chmod 755 /usr/bin/node
    chown root:root /usr/bin/node
    ln -sf /usr/bin/node /usr/local/bin/node 2>/dev/null || true
    chmod 755 /usr/local/bin/node
    chmod +x /usr/bin/node

    # Check current node permissions
    NODE_PERMS=$(ls -la /usr/bin/node)
    print_status "Node.js permissions: $NODE_PERMS"

    # Test node access for mainnet user
    if sudo -u "$APP_USER" /usr/bin/node --version >/dev/null 2>&1; then
        print_success "Node.js accessible to $APP_USER"
    else
        print_warning "Node.js not accessible, applying comprehensive fix..."

        # Method 1: Fix file permissions
        chmod 755 /usr/bin/node
        chown root:root /usr/bin/node

        # Method 2: Add to additional groups
        usermod -a -G bin "$APP_USER" 2>/dev/null || true
        usermod -a -G daemon "$APP_USER" 2>/dev/null || true

        # Method 3: Create symlink with proper permissions
        ln -sf /usr/bin/node /usr/local/bin/node 2>/dev/null || true
        chmod 755 /usr/local/bin/node 2>/dev/null || true

        # Method 4: Update sudoers for node execution
        echo "$APP_USER ALL=(root) NOPASSWD: /usr/bin/node" > /etc/sudoers.d/mainnet-node
        chmod 440 /etc/sudoers.d/mainnet-node

        # Test again
        if sudo -u "$APP_USER" /usr/bin/node --version >/dev/null 2>&1; then
            print_success "Node.js access fixed for $APP_USER"
        else
            print_error "Failed to fix Node.js access - PM2 may not work"
        fi
    fi

    # Set additional security restrictions
    print_status "Applying additional security restrictions..."

    # Prevent user from accessing other users' files
    chmod 750 "$INSTALL_DIR" 2>/dev/null || true

    # Set resource limits for the user
    cat >> /etc/security/limits.conf << EOF
# Mainnet pool server limits
$APP_USER soft nproc 1024
$APP_USER hard nproc 2048
$APP_USER soft nofile 4096
$APP_USER hard nofile 8192
$APP_USER soft memlock 64
$APP_USER hard memlock 64
EOF

    print_success "Security restrictions applied"
}

# Function to get user input securely
get_user_input() {
    print_status "Gathering configuration information..."

    # First check if we have existing environment and can reuse it
    if [[ -f "$INSTALL_DIR/.env" ]]; then
        print_status "Found existing environment configuration"

        # Extract key information from existing .env
        local existing_domain=$(grep "^CORS_ORIGIN=" "$INSTALL_DIR/.env" 2>/dev/null | cut -d'=' -f2 | sed 's/https:\/\///')
        local existing_base_rpc=$(grep "^BASE_RPC_URL=" "$INSTALL_DIR/.env" 2>/dev/null | cut -d'=' -f2)
        local existing_skale_rpc=$(grep "^SKALE_RPC_URL=" "$INSTALL_DIR/.env" 2>/dev/null | cut -d'=' -f2)
        local existing_contract=$(grep "^POOL_STORAGE_CONTRACT=" "$INSTALL_DIR/.env" 2>/dev/null | cut -d'=' -f2)
        local existing_email=$(grep "^# SSL_EMAIL=" "$INSTALL_DIR/.env" 2>/dev/null | cut -d'=' -f2)

        echo
        print_warning "Existing configuration found:"
        echo "   • Domain: $existing_domain"
        echo "   • Base RPC: $existing_base_rpc"
        echo "   • Skale RPC: $existing_skale_rpc"
        echo "   • Contract: $existing_contract"
        [[ -n "$existing_email" ]] && echo "   • Email: $existing_email"
        echo

        read -p "Do you want to reuse the existing configuration? (Y/n): " -n 1 -r
        echo

        if [[ ! $REPLY =~ ^[Nn]$ ]]; then
            print_success "Reusing existing configuration"

            # Set variables from existing config
            DOMAIN="$existing_domain"
            BASE_RPC="$existing_base_rpc"
            SKALE_RPC="$existing_skale_rpc"
            CONTRACT_ADDRESS="$existing_contract"
            EMAIL="$existing_email"

            # If email is missing, ask for it
            if [[ -z "$EMAIL" ]]; then
                while [[ -z "$EMAIL" ]]; do
                    read -p "Enter your email for SSL certificate: " EMAIL
                    if [[ ! "$EMAIL" =~ ^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$ ]]; then
                        print_error "Invalid email format. Please try again."
                        EMAIL=""
                    fi
                done
            fi

            print_success "Configuration loaded from existing environment"
            return 0
        else
            print_status "Will create new configuration"
        fi
    fi
    
    # Domain name
    while [[ -z "$DOMAIN" ]]; do
        read -p "Enter your domain name (e.g., pool.example.com): " DOMAIN
        if [[ -z "$DOMAIN" ]]; then
            print_error "Domain name cannot be empty. Please enter your domain."
        fi
    done
    
    # Email for SSL certificate
    while [[ -z "$EMAIL" ]]; do
        read -p "Enter your email for SSL certificate: " EMAIL
        if [[ ! "$EMAIL" =~ ^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$ ]]; then
            print_error "Invalid email format. Please try again."
            EMAIL=""
        fi
    done
    
    # Private key (visible input for safe environments)
    while [[ -z "$PRIVATE_KEY" ]]; do
        read -p "Enter your Ethereum private key (0x...): " PRIVATE_KEY
        if [[ ! "$PRIVATE_KEY" =~ ^0x[a-fA-F0-9]{64}$ ]]; then
            print_error "Invalid private key format. Must be 0x followed by 64 hex characters."
            PRIVATE_KEY=""
        fi
    done
    
    # Base RPC URL
    read -p "Enter Base RPC URL [https://mainnet.base.org]: " BASE_RPC
    BASE_RPC=${BASE_RPC:-"https://mainnet.base.org"}
    
    # Skale RPC URL
    read -p "Enter Skale RPC URL [https://mainnet.skalenodes.com/v1/elated-tan-skat]: " SKALE_RPC
    SKALE_RPC=${SKALE_RPC:-"https://mainnet.skalenodes.com/v1/elated-tan-skat"}
    
    # Pool Storage Contract
    read -p "Enter Pool Storage Contract Address [******************************************]: " CONTRACT_ADDRESS
    CONTRACT_ADDRESS=${CONTRACT_ADDRESS:-"******************************************"}
    
    print_success "Configuration information collected"
}

# Function to install application
install_application() {
    print_status "Installing application to $INSTALL_DIR..."
    # Create installation directory
    mkdir -p "$INSTALL_DIR"
    
    # Copy application files
    cp -rfp . "$INSTALL_DIR/"
    
    # Create logs directory
    mkdir -p "$INSTALL_DIR/logs"
    
    # Set ownership with strict permissions
    chown -R "$APP_USER:$APP_USER" "$INSTALL_DIR"
    chmod -R 750 "$INSTALL_DIR"

    # Create restricted directories
    mkdir -p "$INSTALL_DIR"/{logs,tmp}
    chown "$APP_USER:mainnet-logs" "$INSTALL_DIR/logs"
    chmod 750 "$INSTALL_DIR/logs"
    chmod 700 "$INSTALL_DIR/tmp"

    # Install npm dependencies
    cd "$INSTALL_DIR"
    print_status "Installing npm dependencies in $INSTALL_DIR..."
    sudo -u "$APP_USER" npm install --production >> "$LOG_FILE" 2>&1
    
    # Create PM2 ecosystem configuration
    print_status "Creating PM2 ecosystem configuration..."
    cat > "$INSTALL_DIR/ecosystem.config.js" << 'EOF'
module.exports = {
  apps: [
    {
      name: 'mainnet-pool-server',
      script: 'src/server.js',
      instances: 1,
      exec_mode: 'fork',

      // Environment
      env: {
        NODE_ENV: 'development',
        PORT: 3001
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 3001
      },

      // Logging
      log_file: 'logs/pm2-combined.log',
      out_file: 'logs/pm2-out.log',
      error_file: 'logs/pm2-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,

      // Process management
      max_memory_restart: '500M',
      min_uptime: '10s',
      max_restarts: 10,
      restart_delay: 4000,

      // Monitoring
      monitoring: false,
      pmx: false,

      // Auto restart on file changes (development only)
      watch: false,
      ignore_watch: [
        'node_modules',
        'logs',
        '.git',
        '*.log'
      ],

      // Advanced PM2 features
      kill_timeout: 5000,
      listen_timeout: 3000,
      shutdown_with_message: true,

      // Health check
      health_check_grace_period: 3000,

      // Source map support
      source_map_support: true,

      // Graceful shutdown
      kill_retry_time: 100,

      // Environment variables
      env_file: '.env'
    }
  ]
};
EOF

    print_success "Application installed to $INSTALL_DIR"
}

# Function to configure environment
configure_environment() {
    print_status "Configuring environment variables..."

    # Clear old wallet data and detect encryption format issues
    clear_old_wallet_data

    # Check if we need to handle old encryption format
    local needs_reencryption=false
    if detect_old_encryption_format; then
        needs_reencryption=true
        print_warning "Old encryption format detected. Private key will be re-encrypted."
    fi

    # Generate secure secrets
    ENCRYPTION_KEY=$(openssl rand -hex 32)
    MASTER_PASSWORD=$(openssl rand -base64 32)
    JWT_SECRET=$(openssl rand -base64 64)
    SESSION_SECRET=$(openssl rand -base64 64)

    # Generate Redis password with only alphanumeric characters (no special chars)
    REDIS_PASSWORD=$(openssl rand -hex 16 | tr '[:lower:]' '[:upper:]' | head -c 24)
    
    # Encrypt private key using modern crypto API (with salt for consistency with keyManager)
    cd "$INSTALL_DIR"
    ENCRYPTED_PRIVATE_KEY=$(node -e "
        const crypto = require('crypto');

        // Modern encryption for setup - matches keyManager format
        const algorithm = 'aes-256-gcm';
        const salt = crypto.randomBytes(32);
        const key = crypto.pbkdf2Sync('$MASTER_PASSWORD', salt, 100000, 32, 'sha512');
        const iv = crypto.randomBytes(16);
        const cipher = crypto.createCipheriv(algorithm, key, iv);

        const encrypted = Buffer.concat([
            cipher.update('$PRIVATE_KEY', 'utf8'),
            cipher.final()
        ]);
        const tag = cipher.getAuthTag();

        const combined = Buffer.concat([salt, iv, tag, encrypted]);
        console.log(combined.toString('base64'));
    ")
    
    # Create .env file
    cat > "$INSTALL_DIR/.env" << EOF
# Installation Information (for reuse)
# SSL_EMAIL=$EMAIL
# INSTALL_DATE=$(date)

# Server Configuration
NODE_ENV=production
PORT=$INTERNAL_PORT
HOST=127.0.0.1

# Security Configuration
ENCRYPTION_KEY=$ENCRYPTION_KEY
MASTER_PASSWORD=$MASTER_PASSWORD
JWT_SECRET=$JWT_SECRET
SESSION_SECRET=$SESSION_SECRET
ENCRYPTED_PRIVATE_KEY=$ENCRYPTED_PRIVATE_KEY

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
SLOW_DOWN_DELAY_AFTER=50
SLOW_DOWN_DELAY_MS=500

# Blockchain Configuration
BASE_RPC_URL=$BASE_RPC
SKALE_RPC_URL=$SKALE_RPC
POOL_STORAGE_CONTRACT=$CONTRACT_ADDRESS

# Gas Configuration
GAS_LIMIT=500000
GAS_PRICE_MULTIPLIER=1.2
MAX_GAS_PRICE=50000000000

# Redis Configuration
REDIS_URL=redis://:$REDIS_PASSWORD@localhost:6379
REDIS_PASSWORD=$REDIS_PASSWORD

# Monitoring and Logging
LOG_LEVEL=info
LOG_MAX_SIZE=20m
LOG_MAX_FILES=14d
ENABLE_REQUEST_LOGGING=true

# Security Headers
CORS_ORIGIN=https://$DOMAIN
TRUSTED_PROXIES=1

# Health Check
HEALTH_CHECK_INTERVAL=30000
EOF
    
    # Set secure permissions for environment file (running as root)
    chmod 600 "$INSTALL_DIR/.env"
    chmod 750 "$INSTALL_DIR"
    
    print_success "Environment configured"
}

# Function to fix security validation for PM2 compatibility
fix_security_validation() {
    print_status "Fixing security validation for PM2 compatibility..."

    # Update security.js to only validate specific environment variables
    sed -i '/const { error } = schema.validate(process.env);/,/}/c\
    // Only validate the specific environment variables we care about\
    const envToValidate = {\
      NODE_ENV: process.env.NODE_ENV,\
      ENCRYPTION_KEY: process.env.ENCRYPTION_KEY,\
      MASTER_PASSWORD: process.env.MASTER_PASSWORD,\
      JWT_SECRET: process.env.JWT_SECRET,\
      ENCRYPTED_PRIVATE_KEY: process.env.ENCRYPTED_PRIVATE_KEY,\
      PORT: process.env.PORT,\
      HOST: process.env.HOST\
    };\
\
    const { error } = schema.validate(envToValidate);\
    if (error) {\
      throw new Error(`Environment validation failed: ${error.details[0].message}`);\
    }\
  }' "$INSTALL_DIR/src/config/security.js"

    print_success "Security validation fixed for PM2 compatibility"
}

# Function to fix express-slow-down warnings
fix_express_slowdown() {
    print_status "Fixing express-slow-down configuration..."

    # Replace the entire slowDown function to fix syntax issues
    sed -i '/slowDownConfig() {/,/^  }/c\
  slowDownConfig() {\
    return slowDown({\
      windowMs: config.rateLimit.windowMs,\
      delayAfter: config.rateLimit.slowDownDelayAfter,\
      delayMs: () => config.rateLimit.slowDownDelayMs,\
      maxDelayMs: 20000,\
      skipFailedRequests: false,\
      skipSuccessfulRequests: false,\
      validate: { delayMs: false }\
    });\
  }' "$INSTALL_DIR/src/middleware/security.js"

    print_success "Express-slow-down configuration fixed"
}

# Function to clear old wallet data and reset Redis
clear_old_wallet_data() {
    print_status "Clearing old wallet data and resetting cache..."

    # Clear Redis data to remove any cached wallet information
    if systemctl is-active --quiet redis-server; then
        # Get Redis password from environment if it exists
        local redis_password=""
        if [ -f "$INSTALL_DIR/.env" ]; then
            redis_password=$(grep "^REDIS_PASSWORD=" "$INSTALL_DIR/.env" 2>/dev/null | cut -d'=' -f2)
        fi

        # Clear Redis cache (alphanumeric password, no special character issues)
        print_status "Clearing Redis cache..."
        if [ -n "$redis_password" ]; then
            # Test connection first
            if redis-cli -a "$redis_password" ping > /dev/null 2>&1; then
                redis-cli -a "$redis_password" FLUSHALL > /dev/null 2>&1
                print_success "Redis cache cleared with authentication"
            else
                # Try without password in case old config exists
                redis-cli FLUSHALL > /dev/null 2>&1 || true
                print_warning "Redis cache cleared without authentication (old config)"
            fi
        else
            # No password configured, try direct connection
            redis-cli FLUSHALL > /dev/null 2>&1 || true
            print_success "Redis cache cleared without authentication"
        fi
    else
        print_warning "Redis not running, skipping cache clear"
    fi

    # Clear any temporary wallet files
    rm -f /tmp/wallet_* 2>/dev/null || true
    rm -f /tmp/encrypted_key_* 2>/dev/null || true

    print_success "Old wallet data cleared"
}

# Function to detect and handle old encryption format
detect_old_encryption_format() {
    if [ -f "$INSTALL_DIR/.env" ]; then
        local existing_key=$(grep "^ENCRYPTED_PRIVATE_KEY=" "$INSTALL_DIR/.env" 2>/dev/null | cut -d'=' -f2)
        if [ -n "$existing_key" ]; then
            print_status "Detecting encryption format of existing private key..."

            # Test if the existing key can be decrypted with the new format
            cd "$INSTALL_DIR"
            local test_result=$(node -e "
                try {
                    const crypto = require('crypto');
                    const combined = Buffer.from('$existing_key', 'base64');

                    // Check if it looks like old format (no salt prefix)
                    if (combined.length < 64) {
                        console.log('OLD_FORMAT');
                    } else {
                        // Try to detect format by length
                        const expectedMinLength = 32 + 16 + 16; // salt + iv + tag (minimum)
                        if (combined.length >= expectedMinLength) {
                            console.log('NEW_FORMAT');
                        } else {
                            console.log('OLD_FORMAT');
                        }
                    }
                } catch (error) {
                    console.log('UNKNOWN_FORMAT');
                }
            " 2>/dev/null)

            if [ "$test_result" = "OLD_FORMAT" ]; then
                print_warning "Detected old encryption format. The private key will be re-encrypted with the new format."
                return 0  # Old format detected
            else
                print_success "Encryption format is compatible"
                return 1  # New format or compatible
            fi
        fi
    fi
    return 1  # No existing key or compatible format
}

# Function to fix Redis connection issues
fix_redis_connection() {
    print_status "Fixing Redis connection configuration..."

    # Update Redis URL to include password in the connection string
    sed -i "s|REDIS_URL=redis://localhost:6379|REDIS_URL=redis://:$REDIS_PASSWORD@localhost:6379|" "$INSTALL_DIR/.env"
    sed -i "s|REDIS_URL=redis://:.*@localhost:6379|REDIS_URL=redis://:$REDIS_PASSWORD@localhost:6379|" "$INSTALL_DIR/.env"

    # Ensure Redis password matches between .env and redis.conf
    local env_password=$(grep "^REDIS_PASSWORD=" "$INSTALL_DIR/.env" | cut -d'=' -f2)

    # Since we're using alphanumeric-only passwords, no escaping needed
    print_status "Updating Redis configuration with new password..."

    # Stop Redis to ensure clean config update
    systemctl stop redis-server 2>/dev/null || true

    # Remove all existing password configurations
    sed -i '/^requirepass /d' /etc/redis/redis.conf
    sed -i '/^# requirepass /d' /etc/redis/redis.conf
    sed -i '/^#requirepass /d' /etc/redis/redis.conf

    # Add the new alphanumeric password
    echo "requirepass $env_password" >> /etc/redis/redis.conf

    # Restart Redis to apply changes
    systemctl start redis-server

    # Wait for Redis to be ready
    sleep 3

    # Test Redis connection with new password
    if redis-cli -a "$env_password" ping > /dev/null 2>&1; then
        print_success "Redis connection configuration fixed and tested"
    else
        print_warning "Redis connection test failed, but configuration updated"
    fi
}

# Function removed - environment reuse now handled in get_user_input

# Function to validate and clean existing Redis installation
validate_existing_redis() {
    print_status "Validating existing Redis installation..."

    # Check if Redis is installed
    if ! command -v redis-server &> /dev/null; then
        print_warning "Redis not found, will be installed"
        return 0
    fi

    # Check if Redis is running
    if systemctl is-active --quiet redis-server; then
        print_status "Found running Redis instance, preparing for reconfiguration..."

        # Try to connect and get info about existing setup
        local redis_info=""
        redis_info=$(redis-cli info server 2>/dev/null | head -5 || echo "Connection failed")

        if [[ "$redis_info" == *"Connection failed"* ]]; then
            print_status "Redis requires authentication or is not accessible"
        else
            print_status "Redis is accessible without authentication"
        fi

        # Stop Redis for clean reconfiguration
        systemctl stop redis-server
        print_success "Stopped existing Redis instance for reconfiguration"
    else
        print_status "Redis is installed but not running"
    fi

    # Check for existing Redis data and configuration
    if [ -f "/etc/redis/redis.conf" ]; then
        print_status "Found existing Redis configuration"

        # Check for existing password configuration
        if grep -q "^requirepass" /etc/redis/redis.conf; then
            print_warning "Found existing Redis password configuration - will be replaced"
        fi
    fi

    return 0
}

# Function to configure Redis
configure_redis() {
    print_status "Configuring Redis..."

    # Validate existing Redis setup first
    validate_existing_redis

    # Stop Redis service first to ensure clean configuration
    systemctl stop redis-server 2>/dev/null || true

    # Backup original Redis config if it doesn't exist
    if [ ! -f "/etc/redis/redis.conf.original" ]; then
        cp /etc/redis/redis.conf /etc/redis/redis.conf.original
    fi

    # Clean up any existing Redis authentication configuration
    print_status "Cleaning up existing Redis authentication..."

    # Remove all existing requirepass lines (commented and uncommented)
    sed -i '/^requirepass /d' /etc/redis/redis.conf
    sed -i '/^# requirepass /d' /etc/redis/redis.conf
    sed -i '/^#requirepass /d' /etc/redis/redis.conf

    # Remove any auth lines that might exist
    sed -i '/^auth /d' /etc/redis/redis.conf
    sed -i '/^# auth /d' /etc/redis/redis.conf

    # Clear any existing Redis data to remove old authentication
    rm -rf /var/lib/redis/dump.rdb 2>/dev/null || true
    rm -rf /var/lib/redis/*.aof 2>/dev/null || true

    # Add the new password (alphanumeric only, no escaping needed)
    echo "requirepass $REDIS_PASSWORD" >> /etc/redis/redis.conf

    # Ensure Redis is configured for security
    echo "bind 127.0.0.1 ::1" >> /etc/redis/redis.conf
    echo "protected-mode yes" >> /etc/redis/redis.conf

    # Enable Redis to start on boot
    systemctl enable redis-server
    systemctl start redis-server

    # Wait for Redis to start
    sleep 5

    # Test Redis connection
    if redis-cli -a "$REDIS_PASSWORD" ping >/dev/null 2>&1; then
        print_success "Redis configured and started successfully"
    else
        print_warning "Redis started but authentication test failed"
        print_status "This may be normal during initial setup"
    fi
}

# Function to setup SSL certificate
setup_ssl() {
    print_status "Setting up SSL certificate for $DOMAIN..."

    # Create webroot directory for certbot
    mkdir -p /var/www/certbot
    chown www-data:www-data /var/www/certbot

    # Create temporary nginx config for certificate validation
    cat > "/etc/nginx/sites-available/temp-certbot" << EOF
server {
    listen 80;
    server_name $DOMAIN;

    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }

    location / {
        return 301 https://\$server_name\$request_uri;
    }
}
EOF

    # Enable temporary config
    ln -sf "/etc/nginx/sites-available/temp-certbot" "/etc/nginx/sites-enabled/temp-certbot"

    # Test nginx configuration and start/reload nginx
    if nginx -t >> "$LOG_FILE" 2>&1; then
        # Start nginx if not running, reload if running
        if systemctl is-active --quiet nginx; then
            systemctl reload nginx
        else
            systemctl start nginx
        fi
    else
        print_error "Nginx configuration test failed"
        exit 1
    fi

    # Get SSL certificate using webroot method
    if certbot certonly --webroot \
        --webroot-path=/var/www/certbot \
        --non-interactive \
        --agree-tos \
        --email "$EMAIL" \
        -d "$DOMAIN" >> "$LOG_FILE" 2>&1; then
        print_success "SSL certificate obtained for $DOMAIN"

        # Remove temporary config
        rm -f "/etc/nginx/sites-enabled/temp-certbot"
        rm -f "/etc/nginx/sites-available/temp-certbot"
    else
        print_error "Failed to obtain SSL certificate"
        print_error "This might be because port 80 is in use by another service"
        print_error "Please ensure $DOMAIN points to this server and port 80 is accessible"
        exit 1
    fi
}

# Function to configure Nginx
configure_nginx() {
    print_status "Configuring Nginx..."

    # Create Nginx configuration
    cat > "$NGINX_SITES_DIR/$SERVICE_NAME" << EOF
# Rate limiting zones
limit_req_zone \$binary_remote_addr zone=api:10m rate=10r/s;
limit_req_zone \$binary_remote_addr zone=join:10m rate=1r/s;
limit_conn_zone \$binary_remote_addr zone=conn_limit_per_ip:10m;

# Upstream configuration
upstream pool_server {
    server 127.0.0.1:$INTERNAL_PORT;
    keepalive 32;
}

# HTTP server (redirect to HTTPS)
server {
    listen 80;
    server_name $DOMAIN;

    # SSL certificate validation (for Let's Encrypt)
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
        try_files \$uri =404;
    }

    # Health check endpoint (allow HTTP for load balancers)
    location /health {
        proxy_pass http://pool_server;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        access_log off;
    }

    # Redirect all other traffic to HTTPS
    location / {
        return 301 https://\$server_name\$request_uri;
    }
}

# HTTPS server
server {
    listen 443 ssl http2;
    server_name $DOMAIN;

    # SSL configuration
    ssl_certificate /etc/letsencrypt/live/$DOMAIN/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/$DOMAIN/privkey.pem;
    ssl_session_timeout 1d;
    ssl_session_cache shared:SSL:50m;
    ssl_session_tickets off;

    # Modern configuration
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # Security headers
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # Connection limits
    limit_conn conn_limit_per_ip 10;

    # Main application
    location / {
        limit_req zone=api burst=20 nodelay;

        proxy_pass http://pool_server;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;

        # Timeouts
        proxy_connect_timeout 5s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # Join endpoint with stricter rate limiting
    location /join {
        limit_req zone=join burst=5 nodelay;

        proxy_pass http://pool_server;
        proxy_http_version 1.1;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;

        # Timeouts for blockchain operations
        proxy_connect_timeout 5s;
        proxy_send_timeout 120s;
        proxy_read_timeout 120s;
    }

    # Health check
    location /health {
        proxy_pass http://pool_server;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        access_log off;
    }
}
EOF

    # Enable the site
    ln -sf "$NGINX_SITES_DIR/$SERVICE_NAME" "$NGINX_ENABLED_DIR/"

    # Remove default site
    rm -f "$NGINX_ENABLED_DIR/default"

    # Test Nginx configuration
    if nginx -t >> "$LOG_FILE" 2>&1; then
        print_success "Nginx configured successfully"
    else
        print_error "Nginx configuration test failed"
        exit 1
    fi
}

# Function to create systemd service
create_systemd_service() {
    print_status "Creating systemd service..."

    cat > "/etc/systemd/system/$SERVICE_NAME.service" << EOF
[Unit]
Description=Mainnet Pool Server
Documentation=https://github.com/your-repo/secure-pool-server
After=network.target redis-server.service
Wants=redis-server.service

[Service]
Type=simple
# Running as root - no User/Group needed
WorkingDirectory=$INSTALL_DIR
Environment=NODE_ENV=production
Environment=PM2_HOME=$INSTALL_DIR/.pm2
ExecStartPre=/usr/bin/pm2 kill
ExecStart=/usr/bin/pm2-runtime start $INSTALL_DIR/ecosystem.config.js --env production
ExecReload=/usr/bin/pm2 reload $INSTALL_DIR/ecosystem.config.js --env production
ExecStop=/usr/bin/pm2 kill
Restart=always
RestartSec=3

# Enhanced security settings
NoNewPrivileges=true
PrivateTmp=true
PrivateDevices=true
ProtectSystem=strict
ProtectHome=true
ProtectKernelTunables=true
ProtectKernelModules=true
ProtectControlGroups=true
RestrictRealtime=true
RestrictSUIDSGID=true
RemoveIPC=true
ReadWritePaths=$INSTALL_DIR/logs $INSTALL_DIR/tmp
ReadOnlyPaths=$INSTALL_DIR
CapabilityBoundingSet=
AmbientCapabilities=

# Network restrictions
RestrictAddressFamilies=AF_INET AF_INET6 AF_UNIX
IPAddressDeny=any
IPAddressAllow=localhost
IPAddressAllow=10.0.0.0/8
IPAddressAllow=**********/12
IPAddressAllow=***********/16

# Resource limits
LimitNOFILE=4096
LimitNPROC=1024
LimitAS=1G
LimitDATA=512M
LimitSTACK=8M
LimitCORE=0

# Restart policy
Restart=always
RestartSec=10
StartLimitInterval=60s
StartLimitBurst=3

[Install]
WantedBy=multi-user.target
EOF

    # Reload systemd and enable service
    systemctl daemon-reload
    systemctl enable "$SERVICE_NAME"

    print_success "Systemd service created and enabled"
}

# Function to create Go libp2p systemd service
create_libp2p_systemd_service() {
    print_status "Creating Go LibP2P systemd service..."

    cat > "/etc/systemd/system/libp2p-service.service" << EOF
[Unit]
Description=Go LibP2P Service for Pool Server
Documentation=https://github.com/libp2p/go-libp2p
After=network.target
Before=mainnet-pool-server.service

[Service]
Type=simple
ExecStart=$INSTALL_DIR/libp2p-service/libp2p-service
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=libp2p-service

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=$INSTALL_DIR/libp2p-service
ProtectKernelTunables=true
ProtectKernelModules=true
ProtectControlGroups=true

# Network restrictions
RestrictAddressFamilies=AF_INET AF_INET6 AF_UNIX

[Install]
WantedBy=multi-user.target
EOF

    # Reload systemd and enable the service
    systemctl daemon-reload
    systemctl enable libp2p-service.service

    print_success "Go LibP2P systemd service created and enabled"
}

# Function to configure firewall
configure_firewall() {
    print_status "Configuring firewall..."

    # Reset UFW to defaults
    ufw --force reset >> "$LOG_FILE" 2>&1

    # Set default policies
    ufw default deny incoming >> "$LOG_FILE" 2>&1
    ufw default allow outgoing >> "$LOG_FILE" 2>&1

    # Allow SSH (be careful not to lock yourself out)
    ufw allow ssh >> "$LOG_FILE" 2>&1

    # Allow HTTP and HTTPS
    ufw allow 80/tcp >> "$LOG_FILE" 2>&1
    ufw allow 443/tcp >> "$LOG_FILE" 2>&1

    # Enable firewall
    ufw --force enable >> "$LOG_FILE" 2>&1

    print_success "Firewall configured"
}

# Function to configure fail2ban
configure_fail2ban() {
    print_status "Configuring fail2ban..."

    # Create custom jail for our application
    cat > "/etc/fail2ban/jail.d/$SERVICE_NAME.conf" << EOF
[nginx-http-auth]
enabled = true
port = http,https
logpath = /var/log/nginx/error.log

[nginx-limit-req]
enabled = true
port = http,https
logpath = /var/log/nginx/error.log
maxretry = 10

[sshd]
enabled = true
port = ssh
logpath = /var/log/auth.log
maxretry = 5
bantime = 3600
EOF

    # Restart fail2ban
    systemctl restart fail2ban
    systemctl enable fail2ban

    print_success "Fail2ban configured"
}

# Function to start services
start_services() {
    print_status "Starting services..."

    # Start Redis
    systemctl start redis-server

    # Start Go LibP2P service first (dependency for main app)
    print_status "Starting Go LibP2P service..."
    systemctl start libp2p-service

    # Wait for Go service to be ready
    sleep 3

    # Test Go service health
    if curl -f http://localhost:30001/health > /dev/null 2>&1; then
        print_success "Go LibP2P service started successfully"
    else
        print_warning "Go LibP2P service may not be ready yet"
    fi

    # Setup PM2 (running as root)
    print_status "Setting up PM2..."

    # Kill any existing PM2 processes and clean up
    pkill -f pm2 2>/dev/null || true
    pm2 kill 2>/dev/null || true
    export PM2_HOME=$INSTALL_DIR/.pm2 && pm2 kill 2>/dev/null || true

    # Remove and recreate PM2 directory
    rm -rf "$INSTALL_DIR/.pm2"
    rm -rf "/root/.pm2" 2>/dev/null || true
    mkdir -p "$INSTALL_DIR/.pm2"
    chmod 755 "$INSTALL_DIR/.pm2"

    # Create symlink so default PM2 commands work
    ln -sf "$INSTALL_DIR/.pm2" "/root/.pm2"

    # Ensure logs directory exists
    mkdir -p "$INSTALL_DIR/logs"

    # Start the application
    cd "$INSTALL_DIR"
    print_status "Testing application startup..."

    # Test manual startup to catch any immediate errors (running as root)
    print_status "Testing application startup..."
    timeout 10s node src/server.js &
    MANUAL_PID=$!
    sleep 5

    if kill -0 $MANUAL_PID 2>/dev/null; then
        print_success "Application starts successfully"
        kill $MANUAL_PID 2>/dev/null || true

        # Now start with PM2
        print_status "Starting with PM2..."
        if pm2 start ecosystem.config.js --env production 2>&1 | tee -a "$LOG_FILE"; then
            # Save PM2 configuration
            pm2 save 2>&1 | tee -a "$LOG_FILE"
            print_success "Application started with PM2"
        else
            print_warning "PM2 start failed, using fallback method"
            nohup node src/server.js > logs/server.log 2>&1 &
            sleep 3
            print_success "Application started with nohup"
        fi
    else
        print_error "Application failed to start manually"
        print_status "Checking for startup errors..."

        # Check recent logs for errors
        if [[ -f "$INSTALL_DIR/logs/combined-$(date +%Y-%m-%d).log" ]]; then
            print_status "Recent application errors:"
            tail -10 "$INSTALL_DIR/logs/combined-$(date +%Y-%m-%d).log" | tee -a "$LOG_FILE"
        fi

        return 1
    fi

    # Start systemd service
    systemctl start "$SERVICE_NAME"

    # Start Nginx
    systemctl start nginx
    systemctl enable nginx

    print_success "All services started"
}

# Function to setup SSL certificate renewal
setup_ssl_renewal() {
    print_status "Setting up SSL certificate auto-renewal..."

    # Create renewal hook to reload nginx
    cat > "/etc/letsencrypt/renewal-hooks/deploy/nginx-reload.sh" << EOF
#!/bin/bash
systemctl reload nginx
EOF

    chmod +x "/etc/letsencrypt/renewal-hooks/deploy/nginx-reload.sh"

    # Update renewal configuration to use webroot
    if [[ -f "/etc/letsencrypt/renewal/$DOMAIN.conf" ]]; then
        print_status "Updating SSL renewal configuration to use webroot method..."

        # Create a backup of the renewal config
        cp "/etc/letsencrypt/renewal/$DOMAIN.conf" "/etc/letsencrypt/renewal/$DOMAIN.conf.backup"

        # Update the renewal config to use webroot method
        sed -i 's/authenticator = standalone/authenticator = webroot/' "/etc/letsencrypt/renewal/$DOMAIN.conf"

        # Remove existing webroot_map section if it exists
        sed -i '/\[webroot_map\]/,/^$/d' "/etc/letsencrypt/renewal/$DOMAIN.conf"

        # Add webroot configuration
        cat >> "/etc/letsencrypt/renewal/$DOMAIN.conf" << EOF

[webroot_map]
$DOMAIN = /var/www/certbot
EOF

        print_success "SSL renewal configuration updated"
    fi

    # Skip renewal test for now - it will work once nginx is properly configured
    print_status "SSL certificate auto-renewal configured"
    print_warning "Note: SSL renewal test skipped - will work once nginx is running"

    print_success "SSL auto-renewal configured"
}

# Function to perform health check
perform_health_check() {
    print_status "Performing health check..."

    # Wait for services to start
    sleep 10

    
    # Retry logic
    attempts=0
    until curl -f http://localhost:$INTERNAL_PORT/health >> "$LOG_FILE" 2>&1 || [ $attempts -ge 5 ]; do
        print_warning "Waiting for server to become healthy... (attempt $((attempts+1)))"
        sleep 3
        ((attempts++))
    done

    if [ $attempts -ge 5 ]; then
        print_error "Local health check failed on port $INTERNAL_PORT"
        return 1
    fi

    if true; then
        print_success "Local health check passed (port $INTERNAL_PORT)"
    else
        print_error "Local health check failed on port $INTERNAL_PORT"
        return 1
    fi

    # Test HTTP redirect
    if curl -I "http://$DOMAIN" 2>/dev/null | grep -q "301\|302"; then
        print_success "HTTP to HTTPS redirect working"
    else
        print_warning "HTTP redirect may not be working properly"
    fi

    # Test HTTPS connection
    if curl -f "https://$DOMAIN/health" >> "$LOG_FILE" 2>&1; then
        print_success "HTTPS health check passed"
    else
        print_error "HTTPS health check failed"
        return 1
    fi

    # Test unauthorized endpoint
    RESPONSE=$(curl -s "https://$DOMAIN/" | grep -o "You are not authorized")
    if [[ "$RESPONSE" == "You are not authorized" ]]; then
        print_success "Main endpoint security check passed"
    else
        print_warning "Main endpoint response unexpected"
    fi

    return 0
}

# Function to display final information
display_final_info() {
    echo
    echo "=============================================="
    echo -e "${GREEN}🎉 INSTALLATION COMPLETED SUCCESSFULLY! 🎉${NC}"
    echo "=============================================="
    echo
    echo -e "${BLUE}📍 Installation Details:${NC}"
    echo "   • Installation Directory: $INSTALL_DIR"
    echo "   • Service Name: $SERVICE_NAME"
    echo "   • Domain: $DOMAIN"
    echo "   • SSL Certificate: Enabled"
    echo
    echo -e "${BLUE}🔗 Access URLs:${NC}"
    echo "   • Main Page: https://$DOMAIN/"
    echo "   • Health Check: https://$DOMAIN/health"
    echo "   • Join Endpoint: https://$DOMAIN/join (POST)"
    echo
    echo -e "${BLUE}🛠️ Management Commands:${NC}"
    echo "   • Check Status: systemctl status $SERVICE_NAME"
    echo "   • View Logs: journalctl -u $SERVICE_NAME -f"
    echo "   • Restart Service: systemctl restart $SERVICE_NAME"
    echo "   • PM2 Status: export PM2_HOME=$INSTALL_DIR/.pm2 && pm2 status"
    echo "   • PM2 Logs: export PM2_HOME=$INSTALL_DIR/.pm2 && pm2 logs"
    echo
    echo -e "${BLUE}📁 Important Files:${NC}"
    echo "   • Environment: $INSTALL_DIR/.env"
    echo "   • Logs: $INSTALL_DIR/logs/"
    echo "   • Nginx Config: $NGINX_SITES_DIR/$SERVICE_NAME"
    echo "   • Service Config: /etc/systemd/system/$SERVICE_NAME.service"
    echo
    echo -e "${YELLOW}⚠️  Security Reminders:${NC}"
    echo "   • Your private key is encrypted and stored securely"
    echo "   • Firewall is configured to allow only necessary ports"
    echo "   • Fail2ban is active for intrusion prevention"
    echo "   • SSL certificate will auto-renew"
    echo "   • Regular security updates are recommended"
    echo
    echo -e "${GREEN}✅ Your secure pool server is now running!${NC}"
    echo
}

# Main installation function
main() {
    echo
    echo "=============================================="
    echo -e "${BLUE}🔒 Secure Pool Server Installation Script${NC}"
    echo "=============================================="
    echo
    echo "This script will install and configure a production-ready"
    echo "secure pool server with SSL support and automatic startup."
    echo
    echo -e "${YELLOW}⚠️  This script requires root privileges and will:${NC}"
    echo "   • Install system dependencies (Node.js, Nginx, Redis, etc.)"
    echo "   • Create system user and service"
    echo "   • Configure SSL certificate with Let's Encrypt"
    echo "   • Set up firewall and security measures"
    echo "   • Install application to $INSTALL_DIR"
    echo
    read -p "Do you want to continue? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "Installation cancelled."
        exit 0
    fi

    # Start installation
    echo "Starting installation..." | tee "$LOG_FILE"
    echo "Installation log: $LOG_FILE"
    echo

    # Run installation steps
    check_root
    check_requirements
    cleanup_previous_installation
    install_dependencies
    # create_app_user - skipped, using root user
    install_nodejs
    get_user_input  # This now checks for existing env FIRST
    install_application
    build_libp2p_service
    configure_environment
    fix_security_validation
    fix_express_slowdown
    fix_redis_connection
    # restore_environment_if_available - removed, now handled in get_user_input
    configure_redis
    setup_ssl
    configure_nginx
    create_systemd_service
    create_libp2p_systemd_service
    # configure_firewall
    configure_fail2ban
    start_services
    setup_ssl_renewal

    # Perform health check
    if perform_health_check; then
        display_final_info
    else
        print_error "Health check failed. Please check the logs and configuration."
        echo "Log file: $LOG_FILE"
        exit 1
    fi
}

# Error handling
trap 'print_error "Installation failed at line $LINENO. Check $LOG_FILE for details."' ERR

# Run main function if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
