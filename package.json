{"name": "secure-pool-server", "version": "1.0.0", "description": "Production-grade secure server for blockchain pool management", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "lint": "eslint src/", "security-audit": "npm audit && snyk test", "pm2:start": "pm2 start ecosystem.config.js", "pm2:stop": "pm2 stop ecosystem.config.js", "pm2:restart": "pm2 restart ecosystem.config.js"}, "dependencies": {"axios": "^1.10.0", "bcrypt": "^6.0.0", "compression": "^1.8.0", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^17.2.0", "ethers": "^6.15.0", "express": "^4.21.2", "express-jwt": "^8.5.1", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^7.1.5", "express-slow-down": "^2.0.1", "express-validator": "^7.0.1", "helmet": "^8.1.0", "hpp": "^0.2.3", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multiformats": "^13.3.7", "node-forge": "^1.3.1", "node-rsa": "^1.1.1", "redis": "^5.6.0", "uuid": "^11.1.0", "web3": "^4.16.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "xss": "^1.0.15"}, "devDependencies": {"eslint": "^8.55.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-security": "^1.7.1", "jest": "^29.7.0", "nodemon": "^3.1.10", "snyk": "^1.1298.0", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["blockchain", "security", "pool", "web3", "express"], "author": "Functionland <<PERSON><PERSON><PERSON>>", "license": "MIT"}