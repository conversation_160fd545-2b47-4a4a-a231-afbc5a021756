const winston = require('winston');
const DailyRotateFile = require('winston-daily-rotate-file');
const config = require('../config/environment');

// Custom log format
const logFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss'
  }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    let log = `${timestamp} [${level.toUpperCase()}]: ${message}`;
    
    if (Object.keys(meta).length > 0) {
      log += ` ${JSON.stringify(meta)}`;
    }
    
    return log;
  })
);

// Console format for development
const consoleFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({
    format: 'HH:mm:ss'
  }),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    let log = `${timestamp} ${level}: ${message}`;
    
    if (Object.keys(meta).length > 0) {
      log += ` ${JSON.stringify(meta, null, 2)}`;
    }
    
    return log;
  })
);

// Create transports array
const transports = [];

// Console transport for development
if (config.server.isDevelopment) {
  transports.push(
    new winston.transports.Console({
      format: consoleFormat,
      level: 'debug'
    })
  );
}

// File transports for production
if (config.server.isProduction) {
  // Error log file
  transports.push(
    new DailyRotateFile({
      filename: 'logs/error-%DATE%.log',
      datePattern: 'YYYY-MM-DD',
      level: 'error',
      format: logFormat,
      maxSize: config.logging.maxSize,
      maxFiles: config.logging.maxFiles,
      zippedArchive: true
    })
  );

  // Combined log file
  transports.push(
    new DailyRotateFile({
      filename: 'logs/combined-%DATE%.log',
      datePattern: 'YYYY-MM-DD',
      format: logFormat,
      maxSize: config.logging.maxSize,
      maxFiles: config.logging.maxFiles,
      zippedArchive: true
    })
  );

  // Security log file for security events
  transports.push(
    new DailyRotateFile({
      filename: 'logs/security-%DATE%.log',
      datePattern: 'YYYY-MM-DD',
      format: logFormat,
      maxSize: config.logging.maxSize,
      maxFiles: config.logging.maxFiles,
      zippedArchive: true,
      level: 'warn'
    })
  );
}

// Create logger instance
const logger = winston.createLogger({
  level: config.logging.level,
  format: logFormat,
  defaultMeta: {
    service: 'pool-server',
    environment: config.server.env,
    version: process.env.npm_package_version || '1.0.0'
  },
  transports,
  exitOnError: false
});

// Handle uncaught exceptions and unhandled rejections
logger.exceptions.handle(
  new winston.transports.File({ 
    filename: 'logs/exceptions.log',
    format: logFormat
  })
);

logger.rejections.handle(
  new winston.transports.File({ 
    filename: 'logs/rejections.log',
    format: logFormat
  })
);

// Security-specific logging methods
logger.security = {
  authFailure: (data) => {
    logger.warn('Authentication failure', { 
      type: 'auth_failure', 
      ...data 
    });
  },
  
  suspiciousActivity: (data) => {
    logger.warn('Suspicious activity detected', { 
      type: 'suspicious_activity', 
      ...data 
    });
  },
  
  accessDenied: (data) => {
    logger.warn('Access denied', { 
      type: 'access_denied', 
      ...data 
    });
  },
  
  rateLimitExceeded: (data) => {
    logger.warn('Rate limit exceeded', { 
      type: 'rate_limit_exceeded', 
      ...data 
    });
  },
  
  blockchainError: (data) => {
    logger.error('Blockchain operation error', { 
      type: 'blockchain_error', 
      ...data 
    });
  },
  
  transactionSuccess: (data) => {
    logger.info('Transaction successful', { 
      type: 'transaction_success', 
      ...data 
    });
  }
};

// Blockchain-specific logging methods
logger.blockchain = {
  transactionSent: (data) => {
    logger.info('Transaction sent', { 
      type: 'transaction_sent', 
      ...data 
    });
  },
  
  transactionConfirmed: (data) => {
    logger.info('Transaction confirmed', { 
      type: 'transaction_confirmed', 
      ...data 
    });
  },
  
  transactionFailed: (data) => {
    logger.error('Transaction failed', { 
      type: 'transaction_failed', 
      ...data 
    });
  },
  
  gasEstimationError: (data) => {
    logger.warn('Gas estimation error', { 
      type: 'gas_estimation_error', 
      ...data 
    });
  },
  
  networkError: (data) => {
    logger.error('Network error', { 
      type: 'network_error', 
      ...data 
    });
  }
};

module.exports = logger;
