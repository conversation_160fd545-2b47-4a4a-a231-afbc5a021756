const { ethers } = require('ethers');

// Use dynamic import for ES modules
let base58btc;

async function initializeBase58() {
  if (!base58btc) {
    const module = await import('multiformats/bases/base58');
    base58btc = module.base58btc;
  }
  return base58btc;
}

/**
 * Convert PeerID to bytes32 format for smart contract usage
 * @param {string} peerId - The peer ID to convert
 * @returns {Promise<string>} - Hex string representation of bytes32
 */
async function peerIdToBytes32(peerId) {
  try {
    const base58 = await initializeBase58();

    // Normalize to multibase format (starts with z)
    if (!peerId.startsWith("z")) {
      peerId = `z${peerId}`;
    }

    const decoded = base58.decode(peerId);
    console.log({ decoded });

    let bytes32 = undefined;

    // CIDv1 (Ed25519 public key) format
    const CID_HEADER = [0x00, 0x24, 0x08, 0x01, 0x12];
    const isCIDv1 = CID_HEADER.every((v, i) => decoded[i] === v);

    if (isCIDv1 && decoded.length >= 37) {
      const pubkey = decoded.slice(decoded.length - 32);
      bytes32 = ethers.hexlify(pubkey);
    }

    // Legacy multihash format
    if (decoded.length === 34 && decoded[0] === 0x12 && decoded[1] === 0x20) {
      const digest = decoded.slice(2);
      bytes32 = ethers.hexlify(digest);
    }

    if (!bytes32) {
      throw new Error(`Unsupported PeerID format or unexpected length: ${decoded.length}`);
    }

    // Reversible check
    const reconstructed = await bytes32ToPeerId(bytes32);
    if (reconstructed !== peerId.slice(1)) {
      throw new Error(`Could not revert the encoded bytes32 back to original PeerID. Got: ${reconstructed}`);
    }

    return bytes32;
  } catch (err) {
    console.error("Failed to convert PeerID to bytes32:", peerId, err);
    throw err;
  }
}

/**
 * Convert bytes32 back to PeerID
 * @param {string} bytes32 - Hex string of bytes32
 * @returns {Promise<string>} - Original peer ID
 */
async function bytes32ToPeerId(bytes32) {
  try {
    const base58 = await initializeBase58();

    // Remove 0x prefix if present
    if (bytes32.startsWith('0x')) {
      bytes32 = bytes32.slice(2);
    }

    // Convert hex to bytes
    const bytes = Buffer.from(bytes32, 'hex');

    // Reconstruct multihash format (legacy)
    const multihash = Buffer.concat([
      Buffer.from([0x12, 0x20]), // sha256 multihash prefix
      bytes
    ]);

    // Encode with base58btc
    const encoded = base58.encode(multihash);

    // Return without the 'z' prefix
    return encoded.slice(1);
  } catch (err) {
    console.error("Failed to convert bytes32 to PeerID:", bytes32, err);
    throw err;
  }
}

module.exports = {
  peerIdToBytes32,
  bytes32ToPeerId
};
