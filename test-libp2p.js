#!/usr/bin/env node

/**
 * LibP2P Connectivity Test Script
 * 
 * This script tests the libp2p connectivity functionality
 * to ensure peer validation is working correctly.
 * 
 * Usage:
 *   node test-libp2p.js <peer-id>
 */

const libp2pService = require('./src/services/libp2p');
const logger = require('./src/utils/logger');

async function testPeerConnectivity(peerId) {
  console.log('🔗 LibP2P Connectivity Test');
  console.log('===========================\n');
  
  if (!peerId) {
    console.error('❌ Error: Please provide a peer ID to test');
    console.log('Usage: node test-libp2p.js <peer-id>');
    process.exit(1);
  }
  
  try {
    console.log(`Testing connectivity to peer: ${peerId.substring(0, 8)}...`);
    console.log('Relay: /dns/relay.dev.fx.land/tcp/4001/p2p/12D3KooWDRrBaAfPwsGJivBoUw5fE7ZpDiyfUjqgiURq2DEcL835/p2p-circuit\n');
    
    // Initialize libp2p service
    console.log('🚀 Initializing libp2p service...');
    await libp2pService.initialize();
    console.log('✅ LibP2P service initialized\n');
    
    // Get initial stats
    const initialStats = libp2pService.getStats();
    console.log('📊 Initial Stats:', {
      initialized: initialStats.initialized,
      connections: initialStats.connections,
      peers: initialStats.peers
    });
    console.log();
    
    // Test peer connectivity
    console.log('🔍 Testing peer connectivity...');
    const startTime = Date.now();
    
    const isReachable = await libp2pService.validatePeerConnectivity(peerId);
    
    const duration = Date.now() - startTime;
    
    if (isReachable) {
      console.log(`✅ SUCCESS: Peer is reachable! (${duration}ms)`);
      console.log('   The peer is online and accessible through the relay network.');
    } else {
      console.log(`❌ FAILED: Peer is not reachable (${duration}ms)`);
      console.log('   The peer may be offline or not connected to the relay network.');
    }
    
    console.log();
    
    // Get final stats
    const finalStats = libp2pService.getStats();
    console.log('📊 Final Stats:', {
      initialized: finalStats.initialized,
      connections: finalStats.connections,
      peers: finalStats.peers
    });
    
    // Health check
    console.log('\n🏥 Health Check:');
    const health = await libp2pService.healthCheck();
    console.log(health);
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
  } finally {
    // Cleanup
    console.log('\n🧹 Cleaning up...');
    await libp2pService.shutdown();
    console.log('✅ LibP2P service stopped');
    
    process.exit(isReachable ? 0 : 1);
  }
}

// Handle command line arguments
const peerId = process.argv[2];

// Handle graceful shutdown
process.on('SIGINT', async () => {
  console.log('\n\n⚠️  Received SIGINT, shutting down gracefully...');
  await libp2pService.shutdown();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('\n\n⚠️  Received SIGTERM, shutting down gracefully...');
  await libp2pService.shutdown();
  process.exit(0);
});

// Run the test
testPeerConnectivity(peerId).catch(console.error);
