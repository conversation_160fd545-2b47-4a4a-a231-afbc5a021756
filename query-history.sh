#!/bin/bash

# Query Join Request History Script
# This script provides examples of how to query the join request history
# using curl commands. These endpoints are only accessible from localhost.

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default server configuration
HOST="localhost"
PORT="${PORT:-3000}"
BASE_URL="http://$HOST:$PORT"

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}  Join Request History Query${NC}"
    echo -e "${BLUE}================================${NC}"
    echo
}

print_usage() {
    echo "Usage: $0 [command] [parameters]"
    echo
    echo "Commands:"
    echo "  recent [limit]              - Get recent requests (default: 50)"
    echo "  peer <peerId> [limit]       - Get requests by peer ID"
    echo "  account <account> [limit]   - Get requests by account address"
    echo "  chain <chain> [limit]       - Get requests by chain (base/skale)"
    echo "  pool <poolId> [limit]       - Get requests by pool ID"
    echo "  stats                       - Get statistics"
    echo "  help                        - Show this help"
    echo
    echo "Examples:"
    echo "  $0 recent 10"
    echo "  $0 peer 12D3KooWABC123..."
    echo "  $0 account 0x1234567890abcdef..."
    echo "  $0 chain base"
    echo "  $0 pool 1"
    echo "  $0 stats"
    echo
}

make_request() {
    local endpoint="$1"
    local description="$2"
    
    echo -e "${YELLOW}Querying: $description${NC}"
    echo "Endpoint: $BASE_URL$endpoint"
    echo
    
    response=$(curl -s -w "\nHTTP_STATUS:%{http_code}" "$BASE_URL$endpoint")
    http_status=$(echo "$response" | grep "HTTP_STATUS:" | cut -d: -f2)
    body=$(echo "$response" | sed '/HTTP_STATUS:/d')
    
    if [ "$http_status" = "200" ]; then
        echo -e "${GREEN}✅ Success${NC}"
        echo "$body" | jq '.' 2>/dev/null || echo "$body"
    else
        echo -e "${RED}❌ Error (HTTP $http_status)${NC}"
        echo "$body" | jq '.' 2>/dev/null || echo "$body"
    fi
    echo
}

# Check if jq is available for JSON formatting
if ! command -v jq &> /dev/null; then
    echo -e "${YELLOW}Note: 'jq' not found. JSON output will not be formatted.${NC}"
    echo "Install jq for better output: sudo apt-get install jq"
    echo
fi

# Check if curl is available
if ! command -v curl &> /dev/null; then
    echo -e "${RED}Error: 'curl' is required but not found.${NC}"
    echo "Install curl: sudo apt-get install curl"
    exit 1
fi

print_header

case "$1" in
    "recent")
        limit="${2:-50}"
        make_request "/local/history/recent?limit=$limit" "Recent $limit requests"
        ;;
    
    "peer")
        if [ -z "$2" ]; then
            echo -e "${RED}Error: Peer ID is required${NC}"
            echo "Usage: $0 peer <peerId> [limit]"
            exit 1
        fi
        peer_id="$2"
        limit="${3:-50}"
        make_request "/local/history/peer/$peer_id?limit=$limit" "Requests by peer ID: $peer_id"
        ;;
    
    "account")
        if [ -z "$2" ]; then
            echo -e "${RED}Error: Account address is required${NC}"
            echo "Usage: $0 account <account> [limit]"
            exit 1
        fi
        account="$2"
        limit="${3:-50}"
        make_request "/local/history/account/$account?limit=$limit" "Requests by account: $account"
        ;;
    
    "chain")
        if [ -z "$2" ]; then
            echo -e "${RED}Error: Chain name is required${NC}"
            echo "Usage: $0 chain <chain> [limit]"
            echo "Supported chains: base, skale"
            exit 1
        fi
        chain="$2"
        limit="${3:-50}"
        make_request "/local/history/chain/$chain?limit=$limit" "Requests by chain: $chain"
        ;;
    
    "pool")
        if [ -z "$2" ]; then
            echo -e "${RED}Error: Pool ID is required${NC}"
            echo "Usage: $0 pool <poolId> [limit]"
            exit 1
        fi
        pool_id="$2"
        limit="${3:-50}"
        make_request "/local/history/pool/$pool_id?limit=$limit" "Requests by pool ID: $pool_id"
        ;;
    
    "stats")
        make_request "/local/history/stats" "Request statistics"
        ;;
    
    "help"|"--help"|"-h")
        print_usage
        ;;
    
    "")
        echo -e "${YELLOW}No command specified. Showing recent requests...${NC}"
        echo
        make_request "/local/history/recent?limit=10" "Recent 10 requests"
        echo
        print_usage
        ;;
    
    *)
        echo -e "${RED}Unknown command: $1${NC}"
        echo
        print_usage
        exit 1
        ;;
esac
