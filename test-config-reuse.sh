#!/bin/bash

# Test script to verify configuration reuse functionality

echo "🧪 Testing Configuration Reuse Functionality"
echo "============================================="

# Create test environment file
TEST_DIR="/tmp/test-mainnet"
mkdir -p "$TEST_DIR"

cat > "$TEST_DIR/.env" << EOF
# Installation Information (for reuse)
# SSL_EMAIL=<EMAIL>
# INSTALL_DATE=2024-07-17 15:30:00

# Server Configuration
NODE_ENV=production
PORT=3001
HOST=127.0.0.1

# Security Configuration
ENCRYPTION_KEY=test123456789abcdef
MASTER_PASSWORD=testpassword123
JWT_SECRET=testjwtsecret
SESSION_SECRET=testsessionsecret
ENCRYPTED_PRIVATE_KEY=testencryptedkey

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Blockchain Configuration
BASE_RPC_URL=https://mainnet.base.org
SKALE_RPC_URL=https://mainnet.skalenodes.com/v1/elated-tan-skat
POOL_STORAGE_CONTRACT=0xf293A6902662DcB09E310254A5e418cb28D71b6b

# Security Headers
CORS_ORIGIN=https://test.example.com

# Health Check
HEALTH_CHECK_INTERVAL=30000
EOF

echo "✅ Created test environment file"

# Test extraction function
echo "🔍 Testing configuration extraction..."

extract_config() {
    local env_file="$1"
    
    if [[ -f "$env_file" ]]; then
        local existing_domain=$(grep "^CORS_ORIGIN=" "$env_file" 2>/dev/null | cut -d'=' -f2 | sed 's/https:\/\///')
        local existing_base_rpc=$(grep "^BASE_RPC_URL=" "$env_file" 2>/dev/null | cut -d'=' -f2)
        local existing_skale_rpc=$(grep "^SKALE_RPC_URL=" "$env_file" 2>/dev/null | cut -d'=' -f2)
        local existing_contract=$(grep "^POOL_STORAGE_CONTRACT=" "$env_file" 2>/dev/null | cut -d'=' -f2)
        local existing_email=$(grep "^# SSL_EMAIL=" "$env_file" 2>/dev/null | cut -d'=' -f2)
        
        echo "   Domain: $existing_domain"
        echo "   Base RPC: $existing_base_rpc"
        echo "   Skale RPC: $existing_skale_rpc"
        echo "   Contract: $existing_contract"
        echo "   Email: $existing_email"
        
        return 0
    fi
    
    return 1
}

if extract_config "$TEST_DIR/.env"; then
    echo "✅ Configuration extraction successful"
else
    echo "❌ Configuration extraction failed"
fi

# Cleanup
rm -rf "$TEST_DIR"
echo "🧹 Cleaned up test files"

echo ""
echo "✅ Configuration reuse functionality test completed"
echo ""
echo "📋 What this means for reinstallation:"
echo "   • Script will detect existing .env file"
echo "   • Extract domain, RPC URLs, and contract address"
echo "   • Ask if you want to reuse existing configuration"
echo "   • Skip input prompts if reusing configuration"
echo "   • Only ask for email if not stored in config"
echo ""
echo "🔄 To test with real installation:"
echo "   1. Run: sudo ./install.sh"
echo "   2. Complete installation"
echo "   3. Run: sudo ./install.sh again"
echo "   4. Should offer to reuse existing configuration"
