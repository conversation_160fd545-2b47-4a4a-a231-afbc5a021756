#!/bin/bash

# Test script for upgrade functionality
# This script tests the upgrade process in a safe way

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "🧪 Testing Upgrade Scripts"
echo "=========================="
echo

# Test 1: Check if upgrade scripts exist
print_status "Test 1: Checking if upgrade scripts exist..."
if [ -f "upgrade.sh" ]; then
    print_success "upgrade.sh found"
else
    print_error "upgrade.sh not found"
fi

if [ -f "upgrade-dev.sh" ]; then
    print_success "upgrade-dev.sh found"
else
    print_error "upgrade-dev.sh not found"
fi

echo

# Test 2: Check script permissions
print_status "Test 2: Checking script permissions..."
if [ -x "upgrade.sh" ]; then
    print_success "upgrade.sh is executable"
else
    print_warning "upgrade.sh is not executable, fixing..."
    chmod +x upgrade.sh
fi

if [ -x "upgrade-dev.sh" ]; then
    print_success "upgrade-dev.sh is executable"
else
    print_warning "upgrade-dev.sh is not executable, fixing..."
    chmod +x upgrade-dev.sh
fi

echo

# Test 3: Check if we're in the right directory
print_status "Test 3: Checking directory structure..."
if [ -f "package.json" ] && [ -f "src/server.js" ]; then
    print_success "Directory structure looks correct"
else
    print_error "This doesn't appear to be the pool server directory"
fi

echo

# Test 4: Check dependencies
print_status "Test 4: Checking required tools..."

tools=("node" "npm" "git" "curl" "rsync")
missing_tools=()

for tool in "${tools[@]}"; do
    if command -v "$tool" &> /dev/null; then
        print_success "$tool is available"
    else
        print_warning "$tool is not available"
        missing_tools+=("$tool")
    fi
done

if [ ${#missing_tools[@]} -gt 0 ]; then
    echo
    print_warning "Missing tools: ${missing_tools[*]}"
    echo "Install missing tools before running upgrade"
fi

echo

# Test 5: Check if it's a git repository
print_status "Test 5: Checking git repository..."
if [ -d ".git" ]; then
    print_success "Git repository detected"
    
    # Check git status
    if git status --porcelain | grep -q .; then
        print_warning "There are uncommitted changes"
        echo "Consider committing or stashing changes before upgrade"
    else
        print_success "Working directory is clean"
    fi
    
    # Check remote
    if git remote -v | grep -q "origin"; then
        print_success "Git remote 'origin' configured"
    else
        print_warning "No git remote 'origin' found"
    fi
else
    print_warning "Not a git repository"
    echo "Manual code updates will be required during upgrade"
fi

echo

# Test 6: Check current application status
print_status "Test 6: Checking application status..."

# Check if package.json has the right dependencies
if grep -q "winston" package.json 2>/dev/null; then
    print_success "Winston logging dependency found"
else
    print_warning "Winston logging dependency not found"
fi

if grep -q "redis" package.json 2>/dev/null; then
    print_success "Redis dependency found"
else
    print_warning "Redis dependency not found"
fi

# Check if new files exist
if [ -f "src/services/requestHistory.js" ]; then
    print_success "Request history service already exists"
else
    print_warning "Request history service not found (will be added during upgrade)"
fi

echo

# Test 7: Check system services (if running as root)
if [[ $EUID -eq 0 ]]; then
    print_status "Test 7: Checking system services (running as root)..."
    
    if systemctl list-units --full -all | grep -q "pool-server.service"; then
        print_success "pool-server.service found"
        
        if systemctl is-active --quiet pool-server; then
            print_success "pool-server service is running"
        else
            print_warning "pool-server service is not running"
        fi
    else
        print_warning "pool-server.service not found"
    fi
    
    if systemctl list-units --full -all | grep -q "libp2p-service.service"; then
        print_success "libp2p-service.service found"
    else
        print_warning "libp2p-service.service not found"
    fi
else
    print_status "Test 7: Skipping system service check (not running as root)"
fi

echo

# Test 8: Check Redis connection
print_status "Test 8: Checking Redis connection..."
if command -v redis-cli &> /dev/null; then
    if [ -f ".env" ]; then
        redis_password=$(grep "^REDIS_PASSWORD=" .env 2>/dev/null | cut -d'=' -f2 || echo "")
        
        if [ -n "$redis_password" ]; then
            if redis-cli -a "$redis_password" ping > /dev/null 2>&1; then
                print_success "Redis connection with password successful"
            else
                print_warning "Redis connection with password failed"
            fi
        else
            if redis-cli ping > /dev/null 2>&1; then
                print_success "Redis connection without password successful"
            else
                print_warning "Redis connection failed"
            fi
        fi
    else
        print_warning ".env file not found, cannot test Redis connection"
    fi
else
    print_warning "redis-cli not available"
fi

echo

# Test 9: Dry run test
print_status "Test 9: Performing dry run test..."
print_status "This would create a backup at: /tmp/pool-server-backup-$(date +%Y%m%d_%H%M%S)"
print_status "This would create a log at: /tmp/upgrade-$(date +%Y%m%d_%H%M%S).log"
print_success "Dry run test completed"

echo

# Summary
print_success "🎯 Upgrade Test Summary"
echo "======================="
echo

if [[ $EUID -eq 0 ]]; then
    echo "✅ Running as root - can use upgrade.sh for production"
    echo "   Usage: ./upgrade.sh"
else
    echo "👤 Running as user - use upgrade-dev.sh for development"
    echo "   Usage: ./upgrade-dev.sh"
fi

echo
echo "📋 Pre-upgrade checklist:"
echo "  □ Backup important data"
echo "  □ Ensure stable internet connection"
echo "  □ Stop any manual processes using the application"
echo "  □ Notify users of maintenance window (if applicable)"
echo
echo "🚀 Ready to upgrade? Run the appropriate script:"
if [[ $EUID -eq 0 ]]; then
    echo "  sudo ./upgrade.sh"
else
    echo "  ./upgrade-dev.sh"
fi
echo
