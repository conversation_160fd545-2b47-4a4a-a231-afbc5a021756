package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/libp2p/go-libp2p"
	"github.com/libp2p/go-libp2p/core/host"
	"github.com/libp2p/go-libp2p/core/peer"
	"github.com/libp2p/go-libp2p/p2p/protocol/ping"
	"github.com/multiformats/go-multiaddr"
)

type LibP2PService struct {
	host host.Host
	ping *ping.PingService
}

type PingRequest struct {
	PeerID string `json:"peerId"`
}

type PingResponse struct {
	Success   bool   `json:"success"`
	Latency   int64  `json:"latency,omitempty"`
	Error     string `json:"error,omitempty"`
	PeerID    string `json:"peerId"`
	Timestamp int64  `json:"timestamp"`
}

type HealthResponse struct {
	Status    string `json:"status"`
	PeerID    string `json:"peerId"`
	Addresses []string `json:"addresses"`
	Timestamp int64  `json:"timestamp"`
}

func NewLibP2PService() (*LibP2PService, error) {
	// Create a new libp2p host
	h, err := libp2p.New(
		libp2p.ListenAddrStrings("/ip4/0.0.0.0/tcp/0"),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create libp2p host: %w", err)
	}

	// Create ping service
	pingService := ping.NewPingService(h)

	log.Printf("LibP2P node created with ID: %s", h.ID().String())
	for _, addr := range h.Addrs() {
		log.Printf("Listening on: %s/p2p/%s", addr, h.ID().String())
	}

	return &LibP2PService{
		host: h,
		ping: pingService,
	}, nil
}

func (s *LibP2PService) PingPeer(ctx context.Context, peerIDStr string) (*PingResponse, error) {
	response := &PingResponse{
		PeerID:    peerIDStr,
		Timestamp: time.Now().Unix(),
	}

	// Parse peer ID
	peerID, err := peer.Decode(peerIDStr)
	if err != nil {
		response.Error = fmt.Sprintf("invalid peer ID: %v", err)
		return response, nil
	}

	// Construct relay address
	relayAddr := "/dns/relay.dev.fx.land/tcp/4001/p2p/12D3KooWDRrBaAfPwsGJivBoUw5fE7ZpDiyfUjqgiURq2DEcL835"
	targetAddr := fmt.Sprintf("%s/p2p-circuit/p2p/%s", relayAddr, peerIDStr)

	// Parse multiaddr
	maddr, err := multiaddr.NewMultiaddr(targetAddr)
	if err != nil {
		response.Error = fmt.Sprintf("invalid multiaddr: %v", err)
		return response, nil
	}

	// Add peer to peerstore
	addrInfo, err := peer.AddrInfoFromP2pAddr(maddr)
	if err != nil {
		response.Error = fmt.Sprintf("failed to parse addr info: %v", err)
		return response, nil
	}

	s.host.Peerstore().AddAddrs(addrInfo.ID, addrInfo.Addrs, time.Minute*10)

	// Attempt to ping with timeout
	pingCtx, cancel := context.WithTimeout(ctx, time.Second*10)
	defer cancel()

	start := time.Now()
	result := <-s.ping.Ping(pingCtx, peerID)
	
	if result.Error != nil {
		response.Error = fmt.Sprintf("ping failed: %v", result.Error)
		return response, nil
	}

	response.Success = true
	response.Latency = time.Since(start).Milliseconds()

	return response, nil
}

func (s *LibP2PService) GetHealth() *HealthResponse {
	addrs := make([]string, len(s.host.Addrs()))
	for i, addr := range s.host.Addrs() {
		addrs[i] = fmt.Sprintf("%s/p2p/%s", addr, s.host.ID().String())
	}

	return &HealthResponse{
		Status:    "healthy",
		PeerID:    s.host.ID().String(),
		Addresses: addrs,
		Timestamp: time.Now().Unix(),
	}
}

func (s *LibP2PService) Close() error {
	return s.host.Close()
}

func main() {
	// Create libp2p service
	service, err := NewLibP2PService()
	if err != nil {
		log.Fatalf("Failed to create libp2p service: %v", err)
	}
	defer service.Close()

	// Setup HTTP handlers
	http.HandleFunc("/ping", func(w http.ResponseWriter, r *http.Request) {
		if r.Method != http.MethodPost {
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
			return
		}

		var req PingRequest
		if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
			http.Error(w, "Invalid JSON", http.StatusBadRequest)
			return
		}

		if req.PeerID == "" {
			http.Error(w, "peerId is required", http.StatusBadRequest)
			return
		}

		ctx, cancel := context.WithTimeout(context.Background(), time.Second*15)
		defer cancel()

		response, err := service.PingPeer(ctx, req.PeerID)
		if err != nil {
			http.Error(w, fmt.Sprintf("Internal error: %v", err), http.StatusInternalServerError)
			return
		}

		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(response)
	})

	http.HandleFunc("/health", func(w http.ResponseWriter, r *http.Request) {
		if r.Method != http.MethodGet {
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
			return
		}

		response := service.GetHealth()
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(response)
	})

	// Start HTTP server
	port := "30001"
	log.Printf("Starting LibP2P HTTP service on port %s", port)
	log.Printf("Endpoints:")
	log.Printf("  POST /ping - Ping a peer ID")
	log.Printf("  GET /health - Service health check")
	
	if err := http.ListenAndServe(":"+port, nil); err != nil {
		log.Fatalf("HTTP server failed: %v", err)
	}
}
