{"abi": [{"inputs": [{"internalType": "uint32", "name": "poolId", "type": "uint32"}, {"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "peerId", "type": "bytes32"}], "name": "addMember", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint32", "name": "poolId", "type": "uint32"}], "name": "pools", "outputs": [{"internalType": "address", "name": "creator", "type": "address"}, {"internalType": "uint32", "name": "id", "type": "uint32"}, {"internalType": "uint32", "name": "maxChallengeResponsePeriod", "type": "uint32"}, {"internalType": "uint32", "name": "memberCount", "type": "uint32"}, {"internalType": "uint32", "name": "maxMembers", "type": "uint32"}, {"internalType": "uint256", "name": "requiredTokens", "type": "uint256"}, {"internalType": "uint256", "name": "minPingTime", "type": "uint256"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "region", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint32", "name": "poolId", "type": "uint32"}, {"internalType": "bytes32", "name": "peerId", "type": "bytes32"}], "name": "isPeerIdMemberOfPool", "outputs": [{"internalType": "bool", "name": "isMember", "type": "bool"}, {"internalType": "address", "name": "member<PERSON><PERSON><PERSON>", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint32", "name": "poolId", "type": "uint32"}], "name": "getPool", "outputs": [{"components": [{"internalType": "address", "name": "creator", "type": "address"}, {"internalType": "uint32", "name": "id", "type": "uint32"}, {"internalType": "uint32", "name": "maxChallengeResponsePeriod", "type": "uint32"}, {"internalType": "uint32", "name": "memberCount", "type": "uint32"}, {"internalType": "uint32", "name": "maxMembers", "type": "uint32"}, {"internalType": "uint256", "name": "requiredTokens", "type": "uint256"}, {"internalType": "uint256", "name": "minPingTime", "type": "uint256"}, {"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "region", "type": "string"}], "internalType": "struct IStoragePool.Pool", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint32", "name": "poolId", "type": "uint32"}, {"internalType": "bytes32", "name": "peerId", "type": "bytes32"}], "name": "getJoinRequest", "outputs": [{"components": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "uint32", "name": "poolId", "type": "uint32"}, {"internalType": "uint32", "name": "timestamp", "type": "uint32"}, {"internalType": "uint32", "name": "index", "type": "uint32"}, {"internalType": "uint128", "name": "approvals", "type": "uint128"}, {"internalType": "uint128", "name": "rejections", "type": "uint128"}, {"internalType": "uint8", "name": "status", "type": "uint8"}, {"internalType": "bytes32", "name": "peerId", "type": "bytes32"}], "internalType": "struct IStoragePool.JoinRequest", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint32", "name": "poolId", "type": "uint32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "bytes32", "name": "peerId", "type": "bytes32"}, {"indexed": false, "internalType": "address", "name": "added<PERSON>y", "type": "address"}], "name": "MemberAdded", "type": "event"}]}