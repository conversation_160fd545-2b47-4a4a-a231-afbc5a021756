#!/bin/bash

# Automatic Application Upgrade Script
# This script safely upgrades the pool server application with minimal downtime

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
INSTALL_DIR="/opt/mainnet"
SERVICE_NAME="pool-server"
LIBP2P_SERVICE_NAME="libp2p-service"
BACKUP_DIR="/tmp/pool-server-backup-$(date +%Y%m%d_%H%M%S)"
LOG_FILE="/tmp/upgrade-$(date +%Y%m%d_%H%M%S).log"
REPO_URL="https://github.com/your-repo/mainnet.git"  # Update this with your actual repo URL

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1" | tee -a "$LOG_FILE"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

# Function to check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        print_error "This script must be run as root"
        exit 1
    fi
}

# Function to check if services exist
check_services() {
    print_status "Checking existing services..."
    
    if ! systemctl list-units --full -all | grep -Fq "$SERVICE_NAME.service"; then
        print_error "Service $SERVICE_NAME not found. Please run install.sh first."
        exit 1
    fi
    
    print_success "Services found"
}

# Function to create backup
create_backup() {
    print_status "Creating backup of current installation..."
    
    mkdir -p "$BACKUP_DIR"
    
    # Backup application files
    if [ -d "$INSTALL_DIR" ]; then
        cp -r "$INSTALL_DIR" "$BACKUP_DIR/app"
        print_success "Application files backed up to $BACKUP_DIR/app"
    fi
    
    # Backup systemd service files
    if [ -f "/etc/systemd/system/$SERVICE_NAME.service" ]; then
        cp "/etc/systemd/system/$SERVICE_NAME.service" "$BACKUP_DIR/"
        print_success "Service file backed up"
    fi
    
    if [ -f "/etc/systemd/system/$LIBP2P_SERVICE_NAME.service" ]; then
        cp "/etc/systemd/system/$LIBP2P_SERVICE_NAME.service" "$BACKUP_DIR/"
        print_success "LibP2P service file backed up"
    fi
    
    # Backup nginx configuration if it exists
    if [ -f "/etc/nginx/sites-available/pool-server" ]; then
        cp "/etc/nginx/sites-available/pool-server" "$BACKUP_DIR/"
        print_success "Nginx configuration backed up"
    fi
    
    print_success "Backup completed: $BACKUP_DIR"
}

# Function to stop services
stop_services() {
    print_status "Stopping services..."
    
    # Stop main service
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        systemctl stop "$SERVICE_NAME"
        print_success "Stopped $SERVICE_NAME"
    fi
    
    # Stop libp2p service
    if systemctl is-active --quiet "$LIBP2P_SERVICE_NAME"; then
        systemctl stop "$LIBP2P_SERVICE_NAME"
        print_success "Stopped $LIBP2P_SERVICE_NAME"
    fi
    
    # Wait a moment for services to fully stop
    sleep 3
}

# Function to update application code
update_application() {
    print_status "Updating application code..."
    
    cd "$INSTALL_DIR"
    
    # Check if it's a git repository
    if [ -d ".git" ]; then
        print_status "Pulling latest changes from git..."
        git fetch origin

        # Determine the default branch (main or master)
        local default_branch=""
        if git show-ref --verify --quiet refs/remotes/origin/main; then
            default_branch="main"
        elif git show-ref --verify --quiet refs/remotes/origin/master; then
            default_branch="master"
        else
            # Fallback: try to detect from remote HEAD
            default_branch=$(git symbolic-ref refs/remotes/origin/HEAD 2>/dev/null | sed 's@^refs/remotes/origin/@@' || echo "master")
        fi

        print_status "Using branch: $default_branch"
        git reset --hard origin/$default_branch
        print_success "Git repository updated"
    else
        print_warning "Not a git repository. Manual code update required."
        print_status "Please ensure the latest code is in $INSTALL_DIR"
        read -p "Press Enter when code is updated..."
    fi
    
    # Update dependencies
    print_status "Updating Node.js dependencies..."
    npm ci --production
    print_success "Dependencies updated"
    
    # Rebuild libp2p service if needed
    if [ -d "libp2p-service" ]; then
        print_status "Rebuilding libp2p service..."
        cd libp2p-service
        if command -v go &> /dev/null; then
            go build -o libp2p-service main.go
            chmod +x libp2p-service
            print_success "LibP2P service rebuilt"
        else
            print_warning "Go not found, skipping libp2p rebuild"
        fi
        cd ..
    fi
}

# Function to run database migrations or updates
run_migrations() {
    print_status "Running any necessary migrations..."
    
    # Clear Redis cache to ensure fresh start with new code
    if systemctl is-active --quiet redis-server; then
        local redis_password=""
        if [ -f "$INSTALL_DIR/.env" ]; then
            redis_password=$(grep "^REDIS_PASSWORD=" "$INSTALL_DIR/.env" 2>/dev/null | cut -d'=' -f2)
        fi
        
        if [ -n "$redis_password" ]; then
            redis-cli -a "$redis_password" FLUSHALL > /dev/null 2>&1 || true
        else
            redis-cli FLUSHALL > /dev/null 2>&1 || true
        fi
        print_success "Redis cache cleared"
    fi
    
    print_success "Migrations completed"
}

# Function to start services
start_services() {
    print_status "Starting services..."
    
    # Reload systemd in case service files changed
    systemctl daemon-reload
    
    # Start libp2p service first
    if [ -f "/etc/systemd/system/$LIBP2P_SERVICE_NAME.service" ]; then
        systemctl start "$LIBP2P_SERVICE_NAME"
        print_success "Started $LIBP2P_SERVICE_NAME"
        sleep 2
    fi
    
    # Start main service
    systemctl start "$SERVICE_NAME"
    print_success "Started $SERVICE_NAME"
    
    # Wait for services to be ready
    sleep 5
}

# Function to verify upgrade
verify_upgrade() {
    print_status "Verifying upgrade..."
    
    # Check if services are running
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        print_success "Main service is running"
    else
        print_error "Main service failed to start"
        return 1
    fi
    
    if [ -f "/etc/systemd/system/$LIBP2P_SERVICE_NAME.service" ]; then
        if systemctl is-active --quiet "$LIBP2P_SERVICE_NAME"; then
            print_success "LibP2P service is running"
        else
            print_warning "LibP2P service not running"
        fi
    fi
    
    # Test health endpoint
    local port=$(grep "^PORT=" "$INSTALL_DIR/.env" 2>/dev/null | cut -d'=' -f2 || echo "3000")
    local health_url="http://localhost:$port/health"
    
    print_status "Testing health endpoint..."
    local max_attempts=10
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "$health_url" > /dev/null 2>&1; then
            print_success "Health check passed"
            break
        else
            print_status "Attempt $attempt/$max_attempts - waiting for service..."
            sleep 3
            ((attempt++))
        fi
    done
    
    if [ $attempt -gt $max_attempts ]; then
        print_error "Health check failed after $max_attempts attempts"
        return 1
    fi
    
    # Test history endpoints
    if curl -s "http://localhost:$port/local/history/stats" > /dev/null 2>&1; then
        print_success "History API is working"
    else
        print_warning "History API test failed (may be normal if no data yet)"
    fi
    
    print_success "Upgrade verification completed"
}

# Function to rollback if needed
rollback() {
    print_error "Upgrade failed. Rolling back..."
    
    # Stop services
    systemctl stop "$SERVICE_NAME" 2>/dev/null || true
    systemctl stop "$LIBP2P_SERVICE_NAME" 2>/dev/null || true
    
    # Restore from backup
    if [ -d "$BACKUP_DIR/app" ]; then
        rm -rf "$INSTALL_DIR"
        cp -r "$BACKUP_DIR/app" "$INSTALL_DIR"
        print_success "Application files restored"
    fi
    
    # Restore service files
    if [ -f "$BACKUP_DIR/$SERVICE_NAME.service" ]; then
        cp "$BACKUP_DIR/$SERVICE_NAME.service" "/etc/systemd/system/"
        print_success "Service file restored"
    fi
    
    if [ -f "$BACKUP_DIR/$LIBP2P_SERVICE_NAME.service" ]; then
        cp "$BACKUP_DIR/$LIBP2P_SERVICE_NAME.service" "/etc/systemd/system/"
        print_success "LibP2P service file restored"
    fi
    
    # Restart services
    systemctl daemon-reload
    systemctl start "$SERVICE_NAME"
    systemctl start "$LIBP2P_SERVICE_NAME" 2>/dev/null || true
    
    print_success "Rollback completed"
    exit 1
}

# Function to cleanup old backups
cleanup_old_backups() {
    print_status "Cleaning up old backups..."
    
    # Keep only the last 5 backups
    find /tmp -name "pool-server-backup-*" -type d -mtime +7 -exec rm -rf {} \; 2>/dev/null || true
    find /tmp -name "upgrade-*.log" -mtime +7 -delete 2>/dev/null || true
    
    print_success "Old backups cleaned up"
}

# Main upgrade function
main() {
    echo
    echo "=============================================="
    echo -e "${BLUE}🚀 Pool Server Automatic Upgrade${NC}"
    echo "=============================================="
    echo
    echo "This script will upgrade the pool server application."
    echo "Backup will be created at: $BACKUP_DIR"
    echo "Log file: $LOG_FILE"
    echo
    
    read -p "Do you want to continue? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "Upgrade cancelled."
        exit 0
    fi
    
    echo "Starting upgrade..." | tee "$LOG_FILE"
    echo "Upgrade started at: $(date)" >> "$LOG_FILE"
    
    # Set trap for rollback on error
    trap rollback ERR
    
    # Run upgrade steps
    check_root
    check_services
    create_backup
    stop_services
    update_application
    run_migrations
    start_services
    
    # Verify upgrade (disable trap for verification)
    trap - ERR
    if verify_upgrade; then
        cleanup_old_backups
        
        echo
        print_success "🎉 Upgrade completed successfully!"
        echo
        echo "Services status:"
        systemctl status "$SERVICE_NAME" --no-pager -l
        echo
        echo "Backup location: $BACKUP_DIR"
        echo "Log file: $LOG_FILE"
        echo
    else
        rollback
    fi
}

# Run main function
main "$@"
