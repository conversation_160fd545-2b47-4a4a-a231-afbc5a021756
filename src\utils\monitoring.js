const logger = require('./logger');
const config = require('../config/environment');

class MonitoringService {
  constructor() {
    this.metrics = {
      requests: {
        total: 0,
        successful: 0,
        failed: 0,
        byEndpoint: {},
        byIP: {}
      },
      transactions: {
        total: 0,
        successful: 0,
        failed: 0,
        by<PERSON>hain: {
          base: { total: 0, successful: 0, failed: 0 },
          skale: { total: 0, successful: 0, failed: 0 }
        }
      },
      security: {
        rateLimitHits: 0,
        bruteForceAttempts: 0,
        invalidRequests: 0,
        suspiciousActivity: 0
      },
      performance: {
        averageResponseTime: 0,
        responseTimeHistory: [],
        memoryUsage: [],
        cpuUsage: []
      }
    };

    this.alerts = {
      highErrorRate: { threshold: 0.1, triggered: false }, // 10% error rate
      highResponseTime: { threshold: 5000, triggered: false }, // 5 seconds
      highMemoryUsage: { threshold: 0.8, triggered: false }, // 80% memory usage
      suspiciousActivity: { threshold: 10, triggered: false }, // 10 suspicious events per minute
      rateLimitExceeded: { threshold: 50, triggered: false } // 50 rate limit hits per minute
    };

    this.startPerformanceMonitoring();
  }

  // Request monitoring
  recordRequest(req, res, responseTime) {
    this.metrics.requests.total++;
    
    const endpoint = req.route ? req.route.path : req.path;
    const ip = req.ip;
    
    // Track by endpoint
    if (!this.metrics.requests.byEndpoint[endpoint]) {
      this.metrics.requests.byEndpoint[endpoint] = { total: 0, successful: 0, failed: 0 };
    }
    this.metrics.requests.byEndpoint[endpoint].total++;
    
    // Track by IP
    if (!this.metrics.requests.byIP[ip]) {
      this.metrics.requests.byIP[ip] = { total: 0, successful: 0, failed: 0 };
    }
    this.metrics.requests.byIP[ip].total++;
    
    // Track success/failure
    if (res.statusCode >= 200 && res.statusCode < 400) {
      this.metrics.requests.successful++;
      this.metrics.requests.byEndpoint[endpoint].successful++;
      this.metrics.requests.byIP[ip].successful++;
    } else {
      this.metrics.requests.failed++;
      this.metrics.requests.byEndpoint[endpoint].failed++;
      this.metrics.requests.byIP[ip].failed++;
    }
    
    // Track response time
    this.recordResponseTime(responseTime);
    
    // Check for alerts
    this.checkAlerts();
  }

  // Transaction monitoring
  recordTransaction(chain, success, details = {}) {
    this.metrics.transactions.total++;
    this.metrics.transactions.byChain[chain].total++;
    
    if (success) {
      this.metrics.transactions.successful++;
      this.metrics.transactions.byChain[chain].successful++;
      
      logger.info('Transaction recorded as successful', {
        chain,
        transactionHash: details.transactionHash,
        gasUsed: details.gasUsed
      });
    } else {
      this.metrics.transactions.failed++;
      this.metrics.transactions.byChain[chain].failed++;
      
      logger.warn('Transaction recorded as failed', {
        chain,
        error: details.error,
        transactionHash: details.transactionHash
      });
    }
    
    this.checkAlerts();
  }

  // Security event monitoring
  recordSecurityEvent(type, details = {}) {
    switch (type) {
      case 'rate_limit':
        this.metrics.security.rateLimitHits++;
        break;
      case 'brute_force':
        this.metrics.security.bruteForceAttempts++;
        break;
      case 'invalid_request':
        this.metrics.security.invalidRequests++;
        break;
      case 'suspicious_activity':
        this.metrics.security.suspiciousActivity++;
        break;
    }
    
    logger.security.suspiciousActivity({
      type,
      ...details,
      timestamp: new Date().toISOString()
    });
    
    this.checkAlerts();
  }

  // Performance monitoring
  recordResponseTime(responseTime) {
    this.metrics.performance.responseTimeHistory.push({
      time: responseTime,
      timestamp: Date.now()
    });
    
    // Keep only last 1000 entries
    if (this.metrics.performance.responseTimeHistory.length > 1000) {
      this.metrics.performance.responseTimeHistory.shift();
    }
    
    // Calculate average response time
    const recentTimes = this.metrics.performance.responseTimeHistory
      .slice(-100) // Last 100 requests
      .map(entry => entry.time);
    
    this.metrics.performance.averageResponseTime = 
      recentTimes.reduce((sum, time) => sum + time, 0) / recentTimes.length;
  }

  startPerformanceMonitoring() {
    setInterval(() => {
      this.recordSystemMetrics();
    }, config.monitoring.healthCheckInterval);
  }

  recordSystemMetrics() {
    const memUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();
    
    this.metrics.performance.memoryUsage.push({
      rss: memUsage.rss,
      heapUsed: memUsage.heapUsed,
      heapTotal: memUsage.heapTotal,
      external: memUsage.external,
      timestamp: Date.now()
    });
    
    // Keep only last 100 entries
    if (this.metrics.performance.memoryUsage.length > 100) {
      this.metrics.performance.memoryUsage.shift();
    }
    
    // Log system metrics periodically
    if (this.metrics.performance.memoryUsage.length % 10 === 0) {
      logger.info('System metrics', {
        memory: {
          rss: `${Math.round(memUsage.rss / 1024 / 1024)}MB`,
          heapUsed: `${Math.round(memUsage.heapUsed / 1024 / 1024)}MB`,
          heapTotal: `${Math.round(memUsage.heapTotal / 1024 / 1024)}MB`
        },
        uptime: `${Math.round(process.uptime())}s`,
        activeHandles: process._getActiveHandles().length,
        activeRequests: process._getActiveRequests().length
      });
    }
  }

  // Alert checking
  checkAlerts() {
    // High error rate alert
    const errorRate = this.metrics.requests.total > 0 
      ? this.metrics.requests.failed / this.metrics.requests.total 
      : 0;
    
    if (errorRate > this.alerts.highErrorRate.threshold && !this.alerts.highErrorRate.triggered) {
      this.triggerAlert('high_error_rate', {
        errorRate: (errorRate * 100).toFixed(2) + '%',
        totalRequests: this.metrics.requests.total,
        failedRequests: this.metrics.requests.failed
      });
      this.alerts.highErrorRate.triggered = true;
    } else if (errorRate <= this.alerts.highErrorRate.threshold) {
      this.alerts.highErrorRate.triggered = false;
    }
    
    // High response time alert
    if (this.metrics.performance.averageResponseTime > this.alerts.highResponseTime.threshold 
        && !this.alerts.highResponseTime.triggered) {
      this.triggerAlert('high_response_time', {
        averageResponseTime: this.metrics.performance.averageResponseTime + 'ms'
      });
      this.alerts.highResponseTime.triggered = true;
    } else if (this.metrics.performance.averageResponseTime <= this.alerts.highResponseTime.threshold) {
      this.alerts.highResponseTime.triggered = false;
    }
    
    // Memory usage alert
    const memUsage = process.memoryUsage();
    const memoryUsagePercent = memUsage.heapUsed / memUsage.heapTotal;
    
    if (memoryUsagePercent > this.alerts.highMemoryUsage.threshold 
        && !this.alerts.highMemoryUsage.triggered) {
      this.triggerAlert('high_memory_usage', {
        memoryUsage: (memoryUsagePercent * 100).toFixed(2) + '%',
        heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024) + 'MB',
        heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024) + 'MB'
      });
      this.alerts.highMemoryUsage.triggered = true;
    } else if (memoryUsagePercent <= this.alerts.highMemoryUsage.threshold) {
      this.alerts.highMemoryUsage.triggered = false;
    }
  }

  triggerAlert(type, details) {
    logger.error(`ALERT: ${type}`, {
      alertType: type,
      ...details,
      timestamp: new Date().toISOString()
    });
    
    // In production, you might want to send alerts to external services
    // like Slack, PagerDuty, or email notifications
  }

  // Get current metrics
  getMetrics() {
    return {
      ...this.metrics,
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      nodeVersion: process.version,
      environment: config.server.env
    };
  }

  // Get health status
  getHealthStatus() {
    const errorRate = this.metrics.requests.total > 0 
      ? this.metrics.requests.failed / this.metrics.requests.total 
      : 0;
    
    const memUsage = process.memoryUsage();
    const memoryUsagePercent = memUsage.heapUsed / memUsage.heapTotal;
    
    const isHealthy = errorRate < 0.1 && 
                     this.metrics.performance.averageResponseTime < 5000 &&
                     memoryUsagePercent < 0.8;
    
    return {
      status: isHealthy ? 'healthy' : 'unhealthy',
      checks: {
        errorRate: {
          status: errorRate < 0.1 ? 'pass' : 'fail',
          value: (errorRate * 100).toFixed(2) + '%'
        },
        responseTime: {
          status: this.metrics.performance.averageResponseTime < 5000 ? 'pass' : 'fail',
          value: this.metrics.performance.averageResponseTime + 'ms'
        },
        memoryUsage: {
          status: memoryUsagePercent < 0.8 ? 'pass' : 'fail',
          value: (memoryUsagePercent * 100).toFixed(2) + '%'
        }
      },
      timestamp: new Date().toISOString()
    };
  }

  // Reset metrics (useful for testing or periodic resets)
  resetMetrics() {
    this.metrics = {
      requests: { total: 0, successful: 0, failed: 0, byEndpoint: {}, byIP: {} },
      transactions: { 
        total: 0, 
        successful: 0, 
        failed: 0,
        byChain: {
          base: { total: 0, successful: 0, failed: 0 },
          skale: { total: 0, successful: 0, failed: 0 }
        }
      },
      security: { rateLimitHits: 0, bruteForceAttempts: 0, invalidRequests: 0, suspiciousActivity: 0 },
      performance: { averageResponseTime: 0, responseTimeHistory: [], memoryUsage: [], cpuUsage: [] }
    };
    
    logger.info('Metrics reset');
  }
}

module.exports = new MonitoringService();
